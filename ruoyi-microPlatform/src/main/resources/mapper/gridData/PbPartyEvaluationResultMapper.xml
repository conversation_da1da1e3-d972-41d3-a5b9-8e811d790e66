<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.PbPartyEvaluationResultMapper">

    <resultMap id="PbPartyEvaluationResultMap" type="PbPartyEvaluationResult">
        <result property="id" column="id"/>
        <result property="resultCode" column="result_code"/>
        <result property="targetId" column="target_id"/>
        <result property="targetName" column="target_name"/>
        <result property="targetType" column="target_type"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="evaluationPeriod" column="evaluation_period"/>
        <result property="evaluationBatch" column="evaluation_batch"/>
        <result property="evaluationYear" column="evaluation_year"/>
        <result property="evaluationMonth" column="evaluation_month"/>
        <result property="totalScore" column="total_score"/>
        <result property="evaluationLevel" column="evaluation_level"/>
        <result property="starLevel" column="star_level"/>
        <result property="dimensionScores" column="dimension_scores"/>
        <result property="evaluationDetail" column="evaluation_detail"/>
        <result property="improvementSuggestions" column="improvement_suggestions"/>
        <result property="evaluatorId" column="evaluator_id"/>
        <result property="evaluatorName" column="evaluator_name"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectVo">
        select id, result_code, target_id, target_name, target_type, dept_id, county, country, town,
               evaluation_period, evaluation_batch, evaluation_year, evaluation_month,
               total_score, evaluation_level, star_level, dimension_scores, evaluation_detail,
               improvement_suggestions, evaluator_id, evaluator_name, audit_status, audit_time,
               create_by, create_time, update_by, update_time
        from pb_party_evaluation_result
    </sql>

    <sql id="queryWhere">
        <where>
            <if test="resultCode != null and resultCode != ''">and result_code = #{resultCode}</if>
            <if test="targetId != null">and target_id = #{targetId}</if>
            <if test="targetName != null and targetName != ''">and target_name like concat('%', #{targetName}, '%')</if>
            <if test="targetType != null and targetType != ''">and target_type = #{targetType}</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="county != null and county != ''">and county = #{county}</if>
            <if test="country != null and country != ''">and country = #{country}</if>
            <if test="town != null and town != ''">and town = #{town}</if>
            <if test="evaluationPeriod != null and evaluationPeriod != ''">and evaluation_period = #{evaluationPeriod}</if>
            <if test="evaluationBatch != null and evaluationBatch != ''">and evaluation_batch = #{evaluationBatch}</if>
            <if test="evaluationYear != null">and evaluation_year = #{evaluationYear}</if>
            <if test="evaluationMonth != null">and evaluation_month = #{evaluationMonth}</if>
            <if test="evaluationLevel != null and evaluationLevel != ''">and evaluation_level = #{evaluationLevel}</if>
            <if test="starLevel != null">and star_level = #{starLevel}</if>
            <if test="evaluatorId != null">and evaluator_id = #{evaluatorId}</if>
            <if test="evaluatorName != null and evaluatorName != ''">and evaluator_name like concat('%', #{evaluatorName}, '%')</if>
            <if test="auditStatus != null">and audit_status = #{auditStatus}</if>
            <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </sql>

    <select id="selectPbPartyEvaluationResultList" parameterType="PbPartyEvaluationResult" resultMap="PbPartyEvaluationResultMap">
        <include refid="selectVo"/>
        <include refid="queryWhere"/>
        order by id desc
    </select>

    <select id="selectPbPartyEvaluationResultById" parameterType="Long" resultMap="PbPartyEvaluationResultMap">
        <include refid="selectVo"/>
        where id = #{id}
    </select>

    <select id="checkPbPartyEvaluationResultUnique" parameterType="PbPartyEvaluationResult" resultMap="PbPartyEvaluationResultMap">
        select id from pb_party_evaluation_result where result_code = #{resultCode}
        <if test="id != null">and id != #{id}</if>
    </select>

    <insert id="insertPbPartyEvaluationResult" parameterType="PbPartyEvaluationResult" useGeneratedKeys="true" keyProperty="id">
        insert into pb_party_evaluation_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resultCode != null">result_code,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="targetType != null">target_type,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="evaluationPeriod != null">evaluation_period,</if>
            <if test="evaluationBatch != null">evaluation_batch,</if>
            <if test="evaluationYear != null">evaluation_year,</if>
            <if test="evaluationMonth != null">evaluation_month,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="evaluationLevel != null">evaluation_level,</if>
            <if test="starLevel != null">star_level,</if>
            <if test="dimensionScores != null">dimension_scores,</if>
            <if test="evaluationDetail != null">evaluation_detail,</if>
            <if test="improvementSuggestions != null">improvement_suggestions,</if>
            <if test="evaluatorId != null">evaluator_id,</if>
            <if test="evaluatorName != null">evaluator_name,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resultCode != null">#{resultCode},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="evaluationPeriod != null">#{evaluationPeriod},</if>
            <if test="evaluationBatch != null">#{evaluationBatch},</if>
            <if test="evaluationYear != null">#{evaluationYear},</if>
            <if test="evaluationMonth != null">#{evaluationMonth},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="evaluationLevel != null">#{evaluationLevel},</if>
            <if test="starLevel != null">#{starLevel},</if>
            <if test="dimensionScores != null">#{dimensionScores},</if>
            <if test="evaluationDetail != null">#{evaluationDetail},</if>
            <if test="improvementSuggestions != null">#{improvementSuggestions},</if>
            <if test="evaluatorId != null">#{evaluatorId},</if>
            <if test="evaluatorName != null">#{evaluatorName},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePbPartyEvaluationResult" parameterType="PbPartyEvaluationResult">
        update pb_party_evaluation_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="resultCode != null">result_code = #{resultCode},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="targetType != null">target_type = #{targetType},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="evaluationPeriod != null">evaluation_period = #{evaluationPeriod},</if>
            <if test="evaluationBatch != null">evaluation_batch = #{evaluationBatch},</if>
            <if test="evaluationYear != null">evaluation_year = #{evaluationYear},</if>
            <if test="evaluationMonth != null">evaluation_month = #{evaluationMonth},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="evaluationLevel != null">evaluation_level = #{evaluationLevel},</if>
            <if test="starLevel != null">star_level = #{starLevel},</if>
            <if test="dimensionScores != null">dimension_scores = #{dimensionScores},</if>
            <if test="evaluationDetail != null">evaluation_detail = #{evaluationDetail},</if>
            <if test="improvementSuggestions != null">improvement_suggestions = #{improvementSuggestions},</if>
            <if test="evaluatorId != null">evaluator_id = #{evaluatorId},</if>
            <if test="evaluatorName != null">evaluator_name = #{evaluatorName},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePbPartyEvaluationResultById" parameterType="Long">
        delete from pb_party_evaluation_result where id = #{id}
    </delete>

    <delete id="deletePbPartyEvaluationResultByIds" parameterType="String">
        delete from pb_party_evaluation_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- Group by star_level for a given evaluation_batch -->
    <select id="groupByStarLevel" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT
            star_level as type,
            COUNT(*) as count
        FROM
            pb_party_evaluation_result
        WHERE
            evaluation_batch = #{evaluationBatch}
        GROUP BY
            star_level
        ORDER BY
            star_level
    </select>
    
    <select id="groupByStarLevelAndTownType" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT
            star_level as type,
            town_type as lable,
            star_level as starLevel,
            COUNT(*) as count
        FROM
            pb_party_evaluation_result
        WHERE
            evaluation_batch = #{evaluationBatch}
            AND town_type IS NOT NULL
            AND town_type != ''
            AND star_level IS NOT NULL
        GROUP BY
            town_type, star_level
        ORDER BY
            town_type, star_level
    </select>
</mapper>
