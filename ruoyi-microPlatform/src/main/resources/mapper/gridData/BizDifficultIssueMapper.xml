<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizDifficultIssueMapper">
    
    <resultMap type="BizDifficultIssue" id="BizDifficultIssueResult">
        <result property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="sourceSystem" column="source_system"/>
        <result property="originalId" column="original_id"/>
        <result property="eventTitle" column="event_title"/>
        <result property="eventDescription" column="event_description"/>
        <result property="difficultReason" column="difficult_reason"/>
        <result property="originalProcess" column="original_process"/>
        <result property="eventMajor" column="event_major"/>
        <result property="eventMinor" column="event_minor"/>
        <result property="eventLocation" column="event_location"/>
        <result property="eventTime" column="event_time"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="status" column="status"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="judgeResult" column="judge_result"/>
        <result property="judgeTime" column="judge_time"/>
        <result property="workOrderId" column="work_order_id"/>
        <result property="accessory" column="accessory"/>
        <result property="userType" column="user_type"/>
        <result property="userName" column="user_name"/>
        <result property="userUid" column="user_uid"/>
        <result property="uidType" column="uid_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBizDifficultIssueVo">
        select id, event_id, source_system, original_id, event_title, event_description, difficult_reason, 
               original_process, event_major, event_minor, event_location, event_time, county, country, 
               town, lat, lng, status, flow_status, judge_result, judge_time, work_order_id, accessory, 
               user_type, user_name, user_uid, uid_type, create_by, create_time, update_by, update_time 
        from biz_difficult_issue
    </sql>

    <select id="selectBizDifficultIssueList" parameterType="BizDifficultIssue" resultMap="BizDifficultIssueResult">
        <include refid="selectBizDifficultIssueVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="sourceSystem != null  and sourceSystem != ''"> and source_system like concat('%', #{sourceSystem}, '%')</if>
            <if test="originalId != null  and originalId != ''"> and original_id = #{originalId}</if>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventMajor != null  and eventMajor != ''"> and event_major like concat('%', #{eventMajor}, '%')</if>
            <if test="eventMinor != null  and eventMinor != ''"> and event_minor = #{eventMinor}</if>
            <if test="county != null  and county != ''"> and county = #{county}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="town != null  and town != ''"> and town = #{town}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="flowStatus != null "> and flow_status = #{flowStatus}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userUid != null  and userUid != ''"> and user_uid = #{userUid}</if>
            <if test="params.beginEventTime != null and params.beginEventTime != ''"> and event_time &gt;= #{params.beginEventTime}</if>
            <if test="params.endEventTime != null and params.endEventTime != ''"> and event_time &lt;= #{params.endEventTime}</if>
            <if test="params.beginJudgeTime != null and params.beginJudgeTime != ''"> and judge_time &gt;= #{params.beginJudgeTime}</if>
            <if test="params.endJudgeTime != null and params.endJudgeTime != ''"> and judge_time &lt;= #{params.endJudgeTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBizDifficultIssueById" parameterType="Long" resultMap="BizDifficultIssueResult">
        <include refid="selectBizDifficultIssueVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBizDifficultIssue" parameterType="BizDifficultIssue" useGeneratedKeys="true" keyProperty="id">
        insert into biz_difficult_issue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="sourceSystem != null">source_system,</if>
            <if test="originalId != null">original_id,</if>
            <if test="eventTitle != null">event_title,</if>
            <if test="eventDescription != null">event_description,</if>
            <if test="difficultReason != null">difficult_reason,</if>
            <if test="originalProcess != null">original_process,</if>
            <if test="eventMajor != null">event_major,</if>
            <if test="eventMinor != null">event_minor,</if>
            <if test="eventLocation != null">event_location,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="status != null">status,</if>
            <if test="flowStatus != null">flow_status,</if>
            <if test="judgeResult != null">judge_result,</if>
            <if test="judgeTime != null">judge_time,</if>
            <if test="workOrderId != null">work_order_id,</if>
            <if test="accessory != null">accessory,</if>
            <if test="userType != null">user_type,</if>
            <if test="userName != null">user_name,</if>
            <if test="userUid != null">user_uid,</if>
            <if test="uidType != null">uid_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="sourceSystem != null">#{sourceSystem},</if>
            <if test="originalId != null">#{originalId},</if>
            <if test="eventTitle != null">#{eventTitle},</if>
            <if test="eventDescription != null">#{eventDescription},</if>
            <if test="difficultReason != null">#{difficultReason},</if>
            <if test="originalProcess != null">#{originalProcess},</if>
            <if test="eventMajor != null">#{eventMajor},</if>
            <if test="eventMinor != null">#{eventMinor},</if>
            <if test="eventLocation != null">#{eventLocation},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="status != null">#{status},</if>
            <if test="flowStatus != null">#{flowStatus},</if>
            <if test="judgeResult != null">#{judgeResult},</if>
            <if test="judgeTime != null">#{judgeTime},</if>
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="userType != null">#{userType},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userUid != null">#{userUid},</if>
            <if test="uidType != null">#{uidType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBizDifficultIssue" parameterType="BizDifficultIssue">
        update biz_difficult_issue
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="sourceSystem != null">source_system = #{sourceSystem},</if>
            <if test="originalId != null">original_id = #{originalId},</if>
            <if test="eventTitle != null">event_title = #{eventTitle},</if>
            <if test="eventDescription != null">event_description = #{eventDescription},</if>
            <if test="difficultReason != null">difficult_reason = #{difficultReason},</if>
            <if test="originalProcess != null">original_process = #{originalProcess},</if>
            <if test="eventMajor != null">event_major = #{eventMajor},</if>
            <if test="eventMinor != null">event_minor = #{eventMinor},</if>
            <if test="eventLocation != null">event_location = #{eventLocation},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="status != null">status = #{status},</if>
            <if test="flowStatus != null">flow_status = #{flowStatus},</if>
            <if test="judgeResult != null">judge_result = #{judgeResult},</if>
            <if test="judgeTime != null">judge_time = #{judgeTime},</if>
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userUid != null">user_uid = #{userUid},</if>
            <if test="uidType != null">uid_type = #{uidType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizDifficultIssueById" parameterType="Long">
        delete from biz_difficult_issue where id = #{id}
    </delete>

    <delete id="deleteBizDifficultIssueByIds" parameterType="String">
        delete from biz_difficult_issue where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 根据状态统计数量 -->
    <select id="countByStatus" resultType="int">
        select count(1) 
        from biz_difficult_issue
        where status = #{status}
    </select>

</mapper>
