<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizComprehensiveEventMapper">

    <resultMap type="BizComprehensiveEvent" id="BizComprehensiveEventResult">
        <result property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceTable" column="source_table"/>
        <result property="eventType" column="event_type"/>
        <result property="eventSource" column="event_source"/>
        <result property="eventLocation" column="event_location"/>
        <result property="eventTitle" column="event_title"/>
        <result property="eventDescription" column="event_description"/>
        <result property="eventMajor" column="event_major"/>
        <result property="eventMinor" column="event_minor"/>
        <result property="urgencyLevel" column="urgency_level"/>
        <result property="eventStartTime" column="event_start_time"/>
        <result property="eventDeadline" column="event_deadline"/>
        <result property="reportUserId" column="report_user_id"/>
        <result property="reportName" column="report_name"/>
        <result property="reportTime" column="report_time"/>
        <result property="reportPhone" column="report_phone"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="eventStatus" column="event_status"/>
        <result property="finishEndTime" column="finish_end_time"/>
        <result property="workStatus" column="work_status"/>
        <result property="currentHandleLevel" column="current_handle_level"/>
        <result property="relatedWorkOrderId" column="related_work_order_id"/>
        <result property="disposalContent" column="disposal_content"/>
        <result property="disposalResult" column="disposal_result"/>
        <result property="accessory" column="accessory"/>
        <result property="userType" column="user_type"/>
        <result property="userName" column="user_name"/>
        <result property="userUid" column="user_uid"/>
        <result property="uidType" column="uid_type"/>
        <result property="isReturnVisit" column="is_return_visit"/>
        <result property="returnVisitScore" column="return_visit_score"/>
        <result property="returnVisitContent" column="return_visit_content"/>
        <result property="returnVisitTime" column="return_visit_time"/>
        <result property="isSatisfaction" column="is_satisfaction"/>
        <result property="satisfaction" column="satisfaction"/>
        <result property="satisfactionTime" column="satisfaction_time"/>
        <result property="isReapply" column="is_reapply"/>
        <result property="reapplyTime" column="reapply_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectBizComprehensiveEventVo">
        select id,
               event_id,
               dept_id,
               county,
               country,
               town,
               grid_id,
               grid_name,
               source_id,
               source_table,
               event_type,
               event_source,
               event_location,
               event_title,
               event_description,
               event_major,
               event_minor,
               urgency_level,
               event_start_time,
               event_deadline,
               report_user_id,
               report_name,
               report_time,
               report_phone,
               lat,
               lng,
               event_status,
               finish_end_time,
               work_status,
               current_handle_level,
               related_work_order_id,
               disposal_content,
               disposal_result,
               accessory,
               user_type,
               user_name,
               user_uid,
               uid_type,
               is_return_visit,
               return_visit_score,
               return_visit_content,
               return_visit_time,
               is_satisfaction,
               satisfaction,
               satisfaction_time,
               is_reapply,
               reapply_time,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from biz_comprehensive_event
    </sql>

    <select id="selectBizComprehensiveEventList" parameterType="BizComprehensiveEvent"
            resultMap="BizComprehensiveEventResult">
        <include refid="selectBizComprehensiveEventVo"/>
        <where>
            <if test="eventId != null  and eventId != ''">and event_id = #{eventId}</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="county != null  and county != ''">and county like concat('%', #{county}, '%')</if>
            <if test="country != null  and country != ''">and country like concat('%', #{country}, '%')</if>
            <if test="town != null  and town != ''">and town like concat('%', #{town}, '%')</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="eventType != null ">and event_type = #{eventType}</if>
            <if test="eventTitle != null  and eventTitle != ''">and event_title like concat('%', #{eventTitle}, '%')
            </if>
            <if test="eventStatus != null ">and event_status = #{eventStatus}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="userUid != null  and userUid != ''">and user_uid = #{userUid}</if>
            <if test="isReturnVisit != null ">and is_return_visit = #{isReturnVisit}</if>
            <if test="isSatisfaction != null ">and is_satisfaction = #{isSatisfaction}</if>
            <if test="isReapply != null ">and is_reapply = #{isReapply}</if>
            <if test="params.beginReportTime != null and params.beginReportTime != '' and params.endReportTime != null and params.endReportTime != ''">
                and report_time between #{params.beginReportTime} and #{params.endReportTime}
            </if>
            <if test="params.beginFinishEndTime != null and params.beginFinishEndTime != '' and params.endFinishEndTime != null and params.endFinishEndTime != ''">
                and finish_end_time between #{params.beginFinishEndTime} and #{params.endFinishEndTime}
            </if>
        </where>
    </select>

    <select id="selectBizComprehensiveEventById" parameterType="Long" resultMap="BizComprehensiveEventResult">
        <include refid="selectBizComprehensiveEventVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizComprehensiveEvent" parameterType="BizComprehensiveEvent" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_comprehensive_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null and county != ''">county,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="town != null and town != ''">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null and gridName != ''">grid_name,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceTable != null and sourceTable != ''">source_table,</if>
            <if test="eventType != null">event_type,</if>
            <if test="eventSource != null and eventSource != ''">event_source,</if>
            <if test="eventLocation != null and eventLocation != ''">event_location,</if>
            <if test="eventTitle != null and eventTitle != ''">event_title,</if>
            <if test="eventDescription != null and eventDescription != ''">event_description,</if>
            <if test="eventMajor != null and eventMajor != ''">event_major,</if>
            <if test="eventMinor != null and eventMinor != ''">event_minor,</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level,</if>
            <if test="eventStartTime != null">event_start_time,</if>
            <if test="eventDeadline != null">event_deadline,</if>
            <if test="reportUserId != null">report_user_id,</if>
            <if test="reportName != null and reportName != ''">report_name,</if>
            <if test="reportTime != null">report_time,</if>
            <if test="reportPhone != null and reportPhone != ''">report_phone,</if>
            <if test="lat != null and lat != ''">lat,</if>
            <if test="lng != null and lng != ''">lng,</if>
            <if test="eventStatus != null">event_status,</if>
            <if test="finishEndTime != null">finish_end_time,</if>
            <if test="workStatus != null and workStatus != ''">work_status,</if>
            <if test="currentHandleLevel != null">current_handle_level,</if>
            <if test="relatedWorkOrderId != null">related_work_order_id,</if>
            <if test="disposalContent != null and disposalContent != ''">disposal_content,</if>
            <if test="disposalResult != null and disposalResult != ''">disposal_result,</if>
            <if test="accessory != null and accessory != ''">accessory,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="userUid != null and userUid != ''">user_uid,</if>
            <if test="uidType != null and uidType != ''">uid_type,</if>
            <if test="isReturnVisit != null">is_return_visit,</if>
            <if test="returnVisitScore != null and returnVisitScore != ''">return_visit_score,</if>
            <if test="returnVisitContent != null and returnVisitContent != ''">return_visit_content,</if>
            <if test="returnVisitTime != null">return_visit_time,</if>
            <if test="isSatisfaction != null">is_satisfaction,</if>
            <if test="satisfaction != null">satisfaction,</if>
            <if test="satisfactionTime != null">satisfaction_time,</if>
            <if test="isReapply != null">is_reapply,</if>
            <if test="reapplyTime != null">reapply_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">#{eventId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null and county != ''">#{county},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="town != null and town != ''">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null and gridName != ''">#{gridName},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceTable != null and sourceTable != ''">#{sourceTable},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="eventSource != null and eventSource != ''">#{eventSource},</if>
            <if test="eventLocation != null and eventLocation != ''">#{eventLocation},</if>
            <if test="eventTitle != null and eventTitle != ''">#{eventTitle},</if>
            <if test="eventDescription != null and eventDescription != ''">#{eventDescription},</if>
            <if test="eventMajor != null and eventMajor != ''">#{eventMajor},</if>
            <if test="eventMinor != null and eventMinor != ''">#{eventMinor},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">#{urgencyLevel},</if>
            <if test="eventStartTime != null">#{eventStartTime},</if>
            <if test="eventDeadline != null">#{eventDeadline},</if>
            <if test="reportUserId != null">#{reportUserId},</if>
            <if test="reportName != null and reportName != ''">#{reportName},</if>
            <if test="reportTime != null">#{reportTime},</if>
            <if test="reportPhone != null and reportPhone != ''">#{reportPhone},</if>
            <if test="lat != null and lat != ''">#{lat},</if>
            <if test="lng != null and lng != ''">#{lng},</if>
            <if test="eventStatus != null">#{eventStatus},</if>
            <if test="finishEndTime != null">#{finishEndTime},</if>
            <if test="workStatus != null and workStatus != ''">#{workStatus},</if>
            <if test="currentHandleLevel != null">#{currentHandleLevel},</if>
            <if test="relatedWorkOrderId != null">#{relatedWorkOrderId},</if>
            <if test="disposalContent != null and disposalContent != ''">#{disposalContent},</if>
            <if test="disposalResult != null and disposalResult != ''">#{disposalResult},</if>
            <if test="accessory != null and accessory != ''">#{accessory},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="userUid != null and userUid != ''">#{userUid},</if>
            <if test="uidType != null and uidType != ''">#{uidType},</if>
            <if test="isReturnVisit != null">#{isReturnVisit},</if>
            <if test="returnVisitScore != null and returnVisitScore != ''">#{returnVisitScore},</if>
            <if test="returnVisitContent != null and returnVisitContent != ''">#{returnVisitContent},</if>
            <if test="returnVisitTime != null">#{returnVisitTime},</if>
            <if test="isSatisfaction != null">#{isSatisfaction},</if>
            <if test="satisfaction != null">#{satisfaction},</if>
            <if test="satisfactionTime != null">#{satisfactionTime},</if>
            <if test="isReapply != null">#{isReapply},</if>
            <if test="reapplyTime != null">#{reapplyTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBizComprehensiveEvent" parameterType="BizComprehensiveEvent">
        update biz_comprehensive_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id = #{eventId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null and county != ''">county = #{county},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="town != null and town != ''">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null and gridName != ''">grid_name = #{gridName},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceTable != null and sourceTable != ''">source_table = #{sourceTable},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="eventSource != null and eventSource != ''">event_source = #{eventSource},</if>
            <if test="eventLocation != null and eventLocation != ''">event_location = #{eventLocation},</if>
            <if test="eventTitle != null and eventTitle != ''">event_title = #{eventTitle},</if>
            <if test="eventDescription != null and eventDescription != ''">event_description = #{eventDescription},</if>
            <if test="eventMajor != null and eventMajor != ''">event_major = #{eventMajor},</if>
            <if test="eventMinor != null and eventMinor != ''">event_minor = #{eventMinor},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level = #{urgencyLevel},</if>
            <if test="eventStartTime != null">event_start_time = #{eventStartTime},</if>
            <if test="eventDeadline != null">event_deadline = #{eventDeadline},</if>
            <if test="reportUserId != null">report_user_id = #{reportUserId},</if>
            <if test="reportName != null and reportName != ''">report_name = #{reportName},</if>
            <if test="reportTime != null">report_time = #{reportTime},</if>
            <if test="reportPhone != null and reportPhone != ''">report_phone = #{reportPhone},</if>
            <if test="lat != null and lat != ''">lat = #{lat},</if>
            <if test="lng != null and lng != ''">lng = #{lng},</if>
            <if test="eventStatus != null">event_status = #{eventStatus},</if>
            <if test="finishEndTime != null">finish_end_time = #{finishEndTime},</if>
            <if test="workStatus != null and workStatus != ''">work_status = #{workStatus},</if>
            <if test="currentHandleLevel != null">current_handle_level = #{currentHandleLevel},</if>
            <if test="relatedWorkOrderId != null">related_work_order_id = #{relatedWorkOrderId},</if>
            <if test="disposalContent != null and disposalContent != ''">disposal_content = #{disposalContent},</if>
            <if test="disposalResult != null and disposalResult != ''">disposal_result = #{disposalResult},</if>
            <if test="accessory != null and accessory != ''">accessory = #{accessory},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="userUid != null and userUid != ''">user_uid = #{userUid},</if>
            <if test="uidType != null and uidType != ''">uid_type = #{uidType},</if>
            <if test="isReturnVisit != null">is_return_visit = #{isReturnVisit},</if>
            <if test="returnVisitScore != null and returnVisitScore != ''">return_visit_score = #{returnVisitScore},
            </if>
            <if test="returnVisitContent != null and returnVisitContent != ''">return_visit_content =
                #{returnVisitContent},
            </if>
            <if test="returnVisitTime != null">return_visit_time = #{returnVisitTime},</if>
            <if test="isSatisfaction != null">is_satisfaction = #{isSatisfaction},</if>
            <if test="satisfaction != null">satisfaction = #{satisfaction},</if>
            <if test="satisfactionTime != null">satisfaction_time = #{satisfactionTime},</if>
            <if test="isReapply != null">is_reapply = #{isReapply},</if>
            <if test="reapplyTime != null">reapply_time = #{reapplyTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizComprehensiveEventById" parameterType="Long">
        delete
        from biz_comprehensive_event
        where id = #{id}
    </delete>

    <select id="selectTop6EventMajors" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.util.Map">
        SELECT 
            TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(t.event_major, ',', numbers.n), ',', -1)) as typeName,
            COUNT(*) as count
        FROM 
            biz_comprehensive_event t
        CROSS JOIN (
            SELECT 1 as n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
            SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
            SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
        ) numbers
        <where>
            numbers.n <![CDATA[ <= ]]> 1 + LENGTH(t.event_major) - LENGTH(REPLACE(t.event_major, ',', ''))
            AND t.event_major IS NOT NULL 
            AND t.event_major != ''
            <if test="dimension != null">
                <choose>
                    <when test="dimension == 1">
                        <!-- 本日 -->
                        AND DATE(t.report_time) = CURDATE()
                    </when>
                    <when test="dimension == 2">
                        <!-- 本周 -->
                        AND YEARWEEK(t.report_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="dimension == 3">
                        <!-- 本月 -->
                        AND DATE_FORMAT(t.report_time, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')
                    </when>
                    <when test="dimension == 4">
                        <!-- 本年 -->
                        AND YEAR(t.report_time) = YEAR(CURDATE())
                    </when>
                </choose>
            </if>
        </where>
        GROUP BY TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(t.event_major, ',', numbers.n), ',', -1))
        ORDER BY count DESC
        LIMIT 6
    </select>

    <delete id="deleteBizComprehensiveEventByIds" parameterType="String">
        delete from biz_comprehensive_event where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- In BizComprehensiveEventMapper.xml -->
    <select id="selectEventTrendByDateRange" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(report_time, '%m-%d') as eventDate,
            CASE WHEN event_type = 3 THEN 'difficult' ELSE 'normal' END as eventType,
            COUNT(*) as count
        FROM
            biz_comprehensive_event
        WHERE
            report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            DATE_FORMAT(report_time, '%Y-%m-%d'),
            CASE WHEN event_type = 3 THEN 'difficult' ELSE 'normal' END
    </select>



    <!-- 获取疑难问题总数 -->
    <select id="countDifficultIssues" resultType="int">
        SELECT COUNT(1)
        FROM biz_comprehensive_event
        WHERE
            1 = 1
          and event_type = 3
          and report_time BETWEEN #{bigdataParam.startTimeBySql} AND #{bigdataParam.endTimeBySql}
    </select>

    <!-- 按来源系统分组统计疑难问题数量 -->
    <select id="countBySourceSystem" resultType="map">
        SELECT
            event_source as sourceSystem,
            COUNT(1) as count
        FROM
            biz_comprehensive_event
        WHERE
            1 = 1
           and  event_type = 3
          and report_time BETWEEN #{bigdataParam.startTimeBySql} AND #{bigdataParam.endTimeBySql}
        GROUP BY
            event_source
    </select>
    
    <!-- 统计所有综合事项数量 -->
    <select id="countAll" resultType="int">
        SELECT COUNT(1) FROM biz_comprehensive_event
    </select>

</mapper>
