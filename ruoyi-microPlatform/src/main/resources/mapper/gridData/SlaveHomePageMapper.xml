<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SlaveHomePageMapper">
    <!-- 12345热线数量 -->
    <select id="countHotlineDetails" resultType="int">
        SELECT COUNT(*)
        FROM db_hotline_detail
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>

    <!-- 市长信箱数量 -->
    <select id="countMayorMailbox" resultType="int">
        SELECT COUNT(*)
        FROM db_mayor_mailbox
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>

    <!-- 运管服事项数量 -->
    <select id="countUmEvents" resultType="int">
        SELECT COUNT(*)
        FROM db_um_event
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>

    <!-- 万人助企数量 -->
    <select id="countEnterpriseAppeals" resultType="int">
        SELECT COUNT(*)
        FROM db_enterprise_appeal
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>

    <!-- 应急局事项数量 -->
    <select id="countEmergencyEvents" resultType="int">
        SELECT COUNT(*)
        FROM db_emergency_event
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>

    <!-- 信访数量 -->
    <select id="countPetitionInfos" resultType="int">
        SELECT COUNT(*)
        FROM db_petition_info
        WHERE create_time BETWEEN #{param.startTimeBySql} AND #{param.endTimeBySql}
    </select>
</mapper>