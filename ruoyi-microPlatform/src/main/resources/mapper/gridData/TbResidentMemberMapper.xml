<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbResidentMemberMapper">

    <resultMap type="TbResidentMember" id="TbResidentMemberResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="residentId" column="resident_id"/>
        <result property="householdRelation" column="household_relation"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="nation" column="nation"/>
        <result property="birthday" column="birthday" jdbcType="DATE"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="educationBackground" column="education_background"/>
        <result property="registerResidence" column="register_residence"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="idCard" column="id_card"/>
        <result property="jobUnit" column="job_unit"/>
        <result property="partyOrganizationLocation" column="party_organization_location"/>
        <result property="talent" column="talent"/>
        <result property="phone" column="phone"/>
        <result property="oldPhone" column="phone"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="socialOrg" column="social_org"/>
        <result property="age" column="age"/>
        <result property="sitePlatform" column="site_platform"/>
        <result property="siteId" column="site_id"/>
        <result property="associateId" column="associate_id"/>
        <result property="associateName" column="associate_name"/>
        <result property="position" column="position"/>
        <result property="hasDirector" column="has_director"/>
        <result property="hasNewGroup" column="has_new_group"/>
        <result property="groupType" column="group_type"/>
        <result property="personLabel" column="person_label"/>
        <result property="communityName" column="community_name"/>
        <result property="geometry" column="geometry"/>
        <result property="buildingNum" column="building_num"/>
        <result property="unitNum" column="unit_num"/>
        <result property="floorNum" column="floor_num"/>
        <result property="roomNum" column="room_num"/>
        <result property="sort" column="sort"/>
        <collection property="personTypes" javaType="java.util.List" resultMap="TypeResult"/>
    </resultMap>

    <resultMap id="TypeResult" type="TbPersonType">
        <id property="id" column="pt_id"/>
        <result property="label" column="label"/>
        <result property="value" column="pt_id"/>
        <result property="text" column="label"/>
    </resultMap>

    <sql id="selectTbResidentMemberVo">
        select rm.id, rm.dept_id, rm.county, rm.country, rm.town, rm.grid_id, rm.grid_name, rm.resident_id, rm.household_relation, rm.name, rm.sex, rm.nation, rm.birthday,
         rm.political_status, rm.education_background, rm.register_residence, rm.marital_status, rm.id_card, rm.job_unit, rm.party_organization_location,rm.age,
         rm.talent, rm.phone, rm.remark, rm.create_by, rm.create_time, rm.update_by, rm.update_time, rm.social_org, rm.site_platform, rm.site_id, rm.associate_id, rm.associate_name,
         rm.position, rm.has_director, rm.has_new_group, rm.group_type, rm.person_label
        from tb_resident_member rm
    </sql>

    <sql id="queryTbResidentMemberVo">
        <where>
            <if test="deptId != null ">and (rm.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = rm.dept_id))</if>
            <if test="county != null  and county != ''">and rm.county = #{county}</if>
            <if test="country != null  and country != ''">and rm.country = #{country}</if>
            <if test="town != null  and town != ''">and rm.town = #{town}</if>
            <if test="gridId != null ">and rm.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and rm.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="label != null ">and rm.label = #{label}</if>
            <if test="gridName != null  and gridName != ''">and rm.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="residentId != null ">and rm.resident_id = #{residentId}</if>
            <if test="communityId != null ">and rm.resident_id in (select id from tb_community_resident where community_id = #{communityId})</if>
            <if test="householdRelation != null  and householdRelation != ''">and rm.household_relation =
                #{householdRelation}
            </if>
            <if test="name != null  and name != ''">and rm.name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''">and rm.sex = #{sex}</if>
            <if test="nation != null  and nation != ''">and rm.nation = #{nation}</if>
            <if test="birthday != null  and birthday != ''">and rm.birthday = #{birthday}</if>
            <if test="politicalStatus != null  and politicalStatus != ''">and rm.political_status = #{politicalStatus}</if>
            <if test="educationBackground != null  and educationBackground != ''">and rm.education_background =
                #{educationBackground}
            </if>
            <if test="registerResidence != null  and registerResidence != ''">and rm.register_residence =
                #{registerResidence}
            </if>
            <if test="maritalStatus != null  and maritalStatus != ''">and rm.marital_status = #{maritalStatus}</if>
            <if test="idCard != null  and idCard != ''">and rm.id_card = #{idCard}</if>
            <if test="jobUnit != null  and jobUnit != ''">and rm.job_unit = #{jobUnit}</if>
            <if test="partyOrganizationLocation != null  and partyOrganizationLocation != ''">and rm.party_organization_location = #{partyOrganizationLocation}</if>
            <if test="talent != null  and talent != ''">and rm.talent = #{talent}</if>
            <if test="phone != null  and phone != ''">and rm.phone = #{phone}</if>
            <if test="socialOrg != null  and socialOrg != ''">and rm.social_org = #{socialOrg}</if>
            <if test="list != null and !list.isEmpty()">
                AND rm.id IN (
                SELECT member_id
                FROM tb_resident_member_type
                WHERE type_id  IN
                <foreach item="label" collection="list" open="(" close=")" separator=",">
                    #{label}
                </foreach>
                GROUP BY member_id
                )
            </if>
            <if test="mainIds != null and !mainIds.isEmpty()">
                AND rm.resident_id IN
                <foreach item="mainId" collection="mainIds" open="(" close=")" separator=",">
                    #{mainId}
                </foreach>
            </if>
            <if test="params.beginAge != null and params.beginAge != '' and params.endAge != null and params.endAge != ''">
                and rm.age between #{params.beginAge} and #{params.endAge}
            </if>
        </where>
    </sql>
<!--    <if test="list != null and !list.isEmpty()">-->
<!--        AND rm.id IN (-->
<!--        SELECT member_id-->
<!--        FROM tb_resident_member_type-->
<!--        WHERE type_id  IN-->
<!--        <foreach item="label" collection="list" open="(" close=")" separator=",">-->
<!--            #{label}-->
<!--        </foreach>-->
<!--        GROUP BY member_id-->
<!--        HAVING COUNT(DISTINCT type_id) = #{list.size}-->
<!--        )-->
<!--    </if>-->
    <select id="selectTbResidentMemberList" parameterType="TbResidentMember" resultMap="TbResidentMemberResult">
        <if test="isExport != null">
            select rm2.id, rm2.dept_id, rm2.county, rm2.country, rm2.town, rm2.grid_id, rm2.grid_name, rm2.resident_id, rm2.household_relation, rm2.name,
            rm2.sex, rm2.nation, rm2.birthday,
            rm2.political_status, rm2.education_background, rm2.register_residence, rm2.marital_status, rm2.id_card, rm2.job_unit,
            rm2.party_organization_location,rm2.age,
            rm2.talent, rm2.phone, rm2.remark, rm2.create_by, rm2.create_time, rm2.update_by, rm2.update_time, rm2.social_org, rm2.site_platform,
            rm2.site_id, rm2.associate_id, rm2.associate_name,
            rm2.position, rm2.has_director, rm2.has_new_group, rm2.group_type, rm2.person_label
            from tb_resident_member rm2
            JOIN (
                SELECT rm.id
                FROM
                tb_resident_member rm
                <if test="country == null">
                    USE INDEX (idx_county_sort_resident_id)
                </if>
                <include refid="queryTbResidentMemberVo"/>
                ORDER BY rm.sort ASC, rm.resident_id DESC, rm.id DESC
                LIMIT #{offset}, #{pageSize}
            )page_ids ON rm2.id = page_ids.id
        </if>
        <if test="isExport == null">
            select rm2.id, rm2.dept_id, rm2.county, rm2.country, rm2.town, rm2.grid_id, rm2.grid_name, rm2.resident_id, rm2.household_relation, rm2.name,
            rm2.sex, rm2.nation, rm2.birthday,
            rm2.political_status, rm2.education_background, rm2.register_residence, rm2.marital_status, rm2.id_card, rm2.job_unit,
            rm2.party_organization_location,rm2.age,
            rm2.talent, rm2.phone, rm2.remark, rm2.create_by, rm2.create_time, rm2.update_by, rm2.update_time, rm2.social_org, rm2.site_platform,
            rm2.site_id, rm2.associate_id, rm2.associate_name,
            rm2.position, rm2.has_director, rm2.has_new_group, rm2.group_type, rm2.person_label,
            co.community_name, co.phone, co.building_num, co.unit_num, co.floor_num, co.room_num
            FROM tb_resident_member rm2
            LEFT JOIN tb_community_resident co ON rm2.resident_id = co.id
            JOIN (
                SELECT rm.id
                FROM tb_resident_member rm
                <if test="country == null">
                    USE INDEX (idx_county_sort_resident_id)
                </if>
                <include refid="queryTbResidentMemberVo"/>
                ORDER BY rm.sort ASC, rm.resident_id DESC, rm.id DESC
                LIMIT #{offset}, #{pageSize}
            ) AS page_ids ON rm2.id = page_ids.id
        </if>
    </select>

    <select id="selectTbResidentMemberCount" parameterType="TbResidentMember" resultType="Long">
        select id from tb_resident_member rm
        <include refid="queryTbResidentMemberVo"/>
    </select>

    <select id="selectTbResidentMemberById" parameterType="Long" resultMap="TbResidentMemberResult">
        select rm.id, rm.dept_id, rm.county, rm.country, rm.town, rm.grid_id, rm.grid_name, rm.resident_id, rm.household_relation, rm.name, rm.sex, rm.nation, rm.birthday,
         rm.political_status, rm.education_background, rm.register_residence, rm.marital_status, rm.id_card, rm.job_unit, rm.party_organization_location,rm.age,
         rm.talent, rm.phone, rm.remark, rm.create_by, rm.create_time, rm.update_by, rm.update_time, rm.social_org, rm.site_platform, rm.site_id, rm.associate_id, rm.associate_name,
         rm.position, rm.has_director, rm.has_new_group, rm.group_type, rm.person_label,
         pt.id as pt_id, pt.label
        from tb_resident_member rm
            left join tb_resident_member_type rmt on rm.id = rmt.member_id
		    left join tb_person_type pt on pt.id = rmt.type_id
        where rm.id = #{id}
    </select>

    <select id="checkTbResidentMemberUnique" parameterType="TbResidentMember" resultMap="TbResidentMemberResult">
        select id from tb_resident_member where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbResidentMember" parameterType="TbResidentMember" useGeneratedKeys="true" keyProperty="id">
        insert into tb_resident_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="residentId != null">resident_id,</if>
            <if test="householdRelation != null">household_relation,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="nation != null">nation,</if>
            <if test="birthday != null">birthday,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="educationBackground != null">education_background,</if>
            <if test="registerResidence != null">register_residence,</if>
            <if test="maritalStatus != null">marital_status,</if>
            <if test="idCard != null">id_card,</if>
            <if test="jobUnit != null">job_unit,</if>
            <if test="hasNewGroup != null">has_new_group,</if>
            <if test="groupType != null">group_type,</if>
            <if test="sitePlatform != null">site_platform,</if>
            <if test="siteId != null">site_id,</if>
            <if test="partyOrganizationLocation != null">party_organization_location,</if>
            <if test="talent != null">talent,</if>
            <if test="phone != null">phone,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="socialOrg != null">social_org,</if>
            <if test="hasDirector != null">has_director,</if>
            <if test="position != null">position,</if>
            <if test="associateId != null">associate_id,</if>
            <if test="associateName != null">associate_name,</if>
            <if test="personLabel != null">person_label,</if>
            <if test="age != null">age,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="residentId != null">#{residentId},</if>
            <if test="householdRelation != null">#{householdRelation},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="nation != null">#{nation},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="educationBackground != null">#{educationBackground},</if>
            <if test="registerResidence != null">#{registerResidence},</if>
            <if test="maritalStatus != null">#{maritalStatus},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="jobUnit != null">#{jobUnit},</if>
            <if test="hasNewGroup != null">#{hasNewGroup},</if>
            <if test="groupType != null">#{groupType},</if>
            <if test="sitePlatform != null">#{sitePlatform},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="partyOrganizationLocation != null">#{partyOrganizationLocation},</if>
            <if test="talent != null">#{talent},</if>
            <if test="phone != null">#{phone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="socialOrg != null">#{socialOrg},</if>
            <if test="hasDirector != null">#{hasDirector},</if>
            <if test="position != null">#{position},</if>
            <if test="associateId != null">#{associateId},</if>
            <if test="associateName != null">#{associateName},</if>
            <if test="personLabel != null">#{personLabel},</if>
            <if test="age != null">#{age},</if>
            <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateTbResidentMember" parameterType="TbResidentMember">
        update tb_resident_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="residentId != null">resident_id = #{residentId},</if>
            <if test="householdRelation != null">household_relation = #{householdRelation},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="educationBackground != null">education_background = #{educationBackground},</if>
            <if test="registerResidence != null">register_residence = #{registerResidence},</if>
            <if test="maritalStatus != null">marital_status = #{maritalStatus},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="jobUnit != null">job_unit = #{jobUnit},</if>
            <if test="hasNewGroup != null">has_new_group = #{hasNewGroup},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="sitePlatform != null">site_platform = #{sitePlatform},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="partyOrganizationLocation != null">party_organization_location = #{partyOrganizationLocation},</if>
            <if test="talent != null">talent = #{talent},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="socialOrg != null">social_org = #{socialOrg},</if>
            <if test="hasDirector != null">has_director = #{hasDirector},</if>
            <if test="position != null">position = #{position},</if>
            <if test="associateId != null">associate_id = #{associateId},</if>
            <if test="associateName != null">associatename = #{associateName},</if>
            <if test="personLabel != null">person_label = #{personLabel},</if>
            <if test="label != null">label = #{label},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>

    </update>

    <delete id="deleteTbResidentMemberById" parameterType="Long">
        delete from tb_resident_member where id = #{id}
    </delete>

    <delete id="deleteTbResidentMemberByIds" parameterType="String">
        delete from tb_resident_member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbResidentMemberByMainIds" parameterType="String">
        delete from tb_resident_member where resident_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchTbResidentMember">
        insert into tb_resident_member( dept_id, county, country, town, grid_id, grid_name, resident_id, household_relation, name, sex, nation, birthday, political_status, education_background, register_residence, marital_status, id_card, job_unit, talent, phone, remark, create_by, create_time, update_by, update_time, social_org, age, sort) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{main.deptId}, #{main.county}, #{main.country}, #{main.town}, #{main.gridId}, #{main.gridName}, #{main.id}, #{item.householdRelation}, #{item.name}, #{item.sex}, #{item.nation}, #{item.birthday}, #{item.politicalStatus}, #{item.educationBackground}, #{item.registerResidence}, #{item.maritalStatus}, #{item.idCard}, #{item.jobUnit}, #{item.talent}, #{item.phone}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.socialOrg}, #{item.age}, #{item.sort})
        </foreach>
    </insert>

    <delete id="deleteTbResidentMemberByResidentId" parameterType="Long">
        delete from tb_resident_member where resident_id = #{residentId}
    </delete>

    <select id="selectResidentMemberByPhone" parameterType="TbResidentMember" resultMap="TbResidentMemberResult">
        select id, grid_id from tb_resident_member
        <where>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="idCard != null and idCard != ''">
                and id_card = #{idCard}
            </if>
        </where>
        order by id desc limit 1
    </select>

    <select id="selectResidentMemberList" parameterType="TbResidentMember" resultMap="TbResidentMemberResult">
        SELECT
        rm.id,
        GROUP_CONCAT(DISTINCT pt.label SEPARATOR ',') AS person_label
        FROM
        tb_resident_member rm
        LEFT JOIN tb_resident_member_type rmt ON rm.id = rmt.member_id
        LEFT JOIN tb_person_type pt ON pt.id = rmt.type_id
        <where>
            <if test="label != null">
                and rm.label = #{label}
            </if>
        </where>
        GROUP BY rm.id
    </select>

    <select id="selectResidentMemberSpaceList" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbResidentMemberResult">
        select rm.id, rm.dept_id, rm.county, rm.country, rm.town, rm.grid_id, rm.grid_name, rm.resident_id, rm.household_relation, rm.name, rm.sex, rm.nation, rm.birthday,
               rm.political_status, rm.education_background, rm.register_residence, rm.marital_status, rm.id_card, rm.job_unit, rm.party_organization_location,rm.age,
               rm.talent, rm.phone, rm.remark, rm.create_by, rm.create_time, rm.update_by, rm.update_time, rm.social_org, rm.site_platform, rm.site_id, rm.associate_id, rm.associate_name,
               rm.position, rm.has_director, rm.has_new_group, rm.group_type, rm.person_label,
               ci.community_name,ci.geometry
        from tb_resident_member rm
        left join tb_community_resident cr on rm.resident_id = cr.id
        left join tb_community_info ci on cr.community_id = ci.id
        <where>
            ci.geometry IS NOT NULL
            and ci.geometry != ''
            <if test="deptId != null ">and (rm.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = rm.dept_id))</if>
        </where>

    </select>

    <update id="updateMemberAge" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo">
        UPDATE tb_resident_member SET age = YEAR(CURDATE()) - YEAR(birthday)
        <if test="id != null">
            where id = #{id}
        </if>
        <if test="residentId != null">
            where resident_id = #{residentId}
        </if>
    </update>

    <select id="countResidentList" resultType="int">
        SELECT COUNT(DISTINCT rm.id)
        FROM tb_resident_member rm
        <include refid="queryTbResidentMemberVo"/>
    </select>

    <select id="selectTbResidentMemberListByMainIds" resultMap="TbResidentMemberResult">
        select
            rm2.id, rm2.dept_id, rm2.county, rm2.country, rm2.town, rm2.grid_id, rm2.grid_name, rm2.resident_id, rm2.household_relation, rm2.name,
            rm2.sex, rm2.nation, rm2.birthday,
            rm2.political_status, rm2.education_background, rm2.register_residence, rm2.marital_status, rm2.id_card, rm2.job_unit,
            rm2.party_organization_location,rm2.age,
            rm2.talent, rm2.phone, rm2.remark, rm2.create_by, rm2.create_time, rm2.update_by, rm2.update_time, rm2.social_org, rm2.site_platform,
            rm2.site_id, rm2.associate_id, rm2.associate_name,
            rm2.position, rm2.has_director, rm2.has_new_group, rm2.group_type, rm2.person_label
        FROM tb_resident_member rm2
        where
            rm2.resident_id IN
            <foreach item="mainId" collection="mainIds" open="(" close=")" separator=",">
                #{mainId}
            </foreach>

    </select>

    <select id="keyPopulationsCount" parameterType="TbResidentMember" resultType="int">
        SELECT
            count(*)
        FROM
            tb_resident_member t1
                LEFT JOIN tb_resident_member_type t2 on t1.id = t2.member_id
                LEFT JOIN tb_person_type t3 on t2.type_id = t3.id
        WHERE t3.is_key = 1
    </select>
    <select id="keyPopulationsCountGroupByType"
            resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT
            t3.label lable,
            count(*) count
        FROM
            tb_resident_member t1
                LEFT JOIN tb_resident_member_type t2 ON t1.id = t2.member_id
                LEFT JOIN tb_person_type t3 ON t2.type_id = t3.id and t3.is_key = 1
        WHERE
            t3.is_key = 1
        GROUP BY
            t3.label
    </select>
</mapper>