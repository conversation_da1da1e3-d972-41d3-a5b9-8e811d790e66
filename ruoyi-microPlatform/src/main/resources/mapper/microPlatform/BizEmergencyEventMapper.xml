<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizEmergencyEventMapper">
    
    <resultMap type="BizEmergencyEvent" id="BizEmergencyEventResult">
        <result property="id" column="id" />
        <result property="eventId" column="event_id" />
        <result property="deptId" column="dept_id" />
        <result property="county" column="county" />
        <result property="country" column="country" />
        <result property="town" column="town" />
        <result property="gridId" column="grid_id" />
        <result property="gridName" column="grid_name" />
        <result property="eventTitle" column="event_title" />
        <result property="eventCategory" column="event_category" />
        <result property="eventLevel" column="event_level" />
        <result property="eventMajor" column="event_major" />
        <result property="eventMinor" column="event_minor" />
        <result property="eventTime" column="event_time" />
        <result property="eventLocation" column="event_location" />
        <result property="lng" column="lng" />
        <result property="lat" column="lat" />
        <result property="eventDescription" column="event_description" />
        <result property="reporterUserId" column="reporter_user_id" />
        <result property="reporterName" column="reporter_name" />
        <result property="reporterPhone" column="reporter_phone" />
        <result property="reporterDeptId" column="reporter_dept_id" />
        <result property="reporterDeptName" column="reporter_dept_name" />
        <result property="isAutoBypass" column="is_auto_bypass" />
        <result property="acceptStatus" column="accept_status" />
        <result property="acceptTime" column="accept_time" />
        <result property="eventSource" column="event_source" />
        <result property="eventReportLevel" column="event_report_level" />
        <result property="currentDeptLevel" column="current_dept_level" />
        <result property="currentDeptId" column="current_dept_id" />
        <result property="eventStatus" column="event_status" />
        <result property="flowStatus" column="flow_status" />
        <result property="relatedWorkOrderId" column="related_work_order_id" />
        <result property="accessory" column="accessory" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectBizEmergencyEventVo">
        select id, event_id, dept_id, county, country, town, grid_id, grid_name, event_title, 
               event_category, event_level, event_major, event_minor, event_time, event_location, 
               lng, lat, event_description, reporter_user_id, reporter_name, reporter_phone, 
               reporter_dept_id, reporter_dept_name, is_auto_bypass, accept_status, accept_time, 
               event_source, event_report_level, current_dept_level, current_dept_id, event_status, 
               flow_status, related_work_order_id, accessory, remark, create_by, create_time, 
               update_by, update_time 
        from biz_emergency_event
    </sql>

    <select id="selectBizEmergencyEventList" parameterType="BizEmergencyEvent" resultMap="BizEmergencyEventResult">
        <include refid="selectBizEmergencyEventVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="county != null  and county != ''"> and county like concat('%', #{county}, '%')</if>
            <if test="country != null  and country != ''"> and country like concat('%', #{country}, '%')</if>
            <if test="town != null  and town != ''"> and town like concat('%', #{town}, '%')</if>
            <if test="gridId != null "> and grid_id = #{gridId}</if>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventCategory != null  and eventCategory != ''"> and event_category = #{eventCategory}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="eventStatus != null "> and event_status = #{eventStatus}</if>
            <if test="acceptStatus != null "> and accept_status = #{acceptStatus}</if>
            <if test="currentDeptId != null  and currentDeptId != ''"> and current_dept_id = #{currentDeptId}</if>
            <if test="params.beginEventTime != null and params.beginEventTime != ''"> and event_time &gt;= #{params.beginEventTime}</if>
            <if test="params.endEventTime != null and params.endEventTime != ''"> and event_time &lt;= #{params.endEventTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBizEmergencyEventById" parameterType="Long" resultMap="BizEmergencyEventResult">
        <include refid="selectBizEmergencyEventVo"/>
        where id = #{id}
    </select>
    
    <select id="countByDeptIdAndStatus" resultType="int">
        select count(1) 
        from biz_emergency_event
        where current_dept_id = #{deptId}
        <if test="eventStatus != null">
            and event_status = #{eventStatus}
        </if>
        <if test="acceptStatus != null">
            and accept_status = #{acceptStatus}
        </if>
    </select>
        
    <insert id="insertBizEmergencyEvent" parameterType="BizEmergencyEvent" useGeneratedKeys="true" keyProperty="id">
        insert into biz_emergency_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="eventTitle != null">event_title,</if>
            <if test="eventCategory != null">event_category,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="eventMajor != null">event_major,</if>
            <if test="eventMinor != null">event_minor,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="eventLocation != null">event_location,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="eventDescription != null">event_description,</if>
            <if test="reporterUserId != null">reporter_user_id,</if>
            <if test="reporterName != null">reporter_name,</if>
            <if test="reporterPhone != null">reporter_phone,</if>
            <if test="reporterDeptId != null">reporter_dept_id,</if>
            <if test="reporterDeptName != null">reporter_dept_name,</if>
            <if test="isAutoBypass != null">is_auto_bypass,</if>
            <if test="acceptStatus != null">accept_status,</if>
            <if test="acceptTime != null">accept_time,</if>
            <if test="eventSource != null">event_source,</if>
            <if test="eventReportLevel != null">event_report_level,</if>
            <if test="currentDeptLevel != null">current_dept_level,</if>
            <if test="currentDeptId != null">current_dept_id,</if>
            <if test="eventStatus != null">event_status,</if>
            <if test="flowStatus != null">flow_status,</if>
            <if test="relatedWorkOrderId != null">related_work_order_id,</if>
            <if test="accessory != null">accessory,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="eventTitle != null">#{eventTitle},</if>
            <if test="eventCategory != null">#{eventCategory},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="eventMajor != null">#{eventMajor},</if>
            <if test="eventMinor != null">#{eventMinor},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="eventLocation != null">#{eventLocation},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="eventDescription != null">#{eventDescription},</if>
            <if test="reporterUserId != null">#{reporterUserId},</if>
            <if test="reporterName != null">#{reporterName},</if>
            <if test="reporterPhone != null">#{reporterPhone},</if>
            <if test="reporterDeptId != null">#{reporterDeptId},</if>
            <if test="reporterDeptName != null">#{reporterDeptName},</if>
            <if test="isAutoBypass != null">#{isAutoBypass},</if>
            <if test="acceptStatus != null">#{acceptStatus},</if>
            <if test="acceptTime != null">#{acceptTime},</if>
            <if test="eventSource != null">#{eventSource},</if>
            <if test="eventReportLevel != null">#{eventReportLevel},</if>
            <if test="currentDeptLevel != null">#{currentDeptLevel},</if>
            <if test="currentDeptId != null">#{currentDeptId},</if>
            <if test="eventStatus != null">#{eventStatus},</if>
            <if test="flowStatus != null">#{flowStatus},</if>
            <if test="relatedWorkOrderId != null">#{relatedWorkOrderId},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateBizEmergencyEvent" parameterType="BizEmergencyEvent">
        update biz_emergency_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="eventTitle != null">event_title = #{eventTitle},</if>
            <if test="eventCategory != null">event_category = #{eventCategory},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="eventMajor != null">event_major = #{eventMajor},</if>
            <if test="eventMinor != null">event_minor = #{eventMinor},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="eventLocation != null">event_location = #{eventLocation},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="eventDescription != null">event_description = #{eventDescription},</if>
            <if test="reporterUserId != null">reporter_user_id = #{reporterUserId},</if>
            <if test="reporterName != null">reporter_name = #{reporterName},</if>
            <if test="reporterPhone != null">reporter_phone = #{reporterPhone},</if>
            <if test="reporterDeptId != null">reporter_dept_id = #{reporterDeptId},</if>
            <if test="reporterDeptName != null">reporter_dept_name = #{reporterDeptName},</if>
            <if test="isAutoBypass != null">is_auto_bypass = #{isAutoBypass},</if>
            <if test="acceptStatus != null">accept_status = #{acceptStatus},</if>
            <if test="acceptTime != null">accept_time = #{acceptTime},</if>
            <if test="eventSource != null">event_source = #{eventSource},</if>
            <if test="eventReportLevel != null">event_report_level = #{eventReportLevel},</if>
            <if test="currentDeptLevel != null">current_dept_level = #{currentDeptLevel},</if>
            <if test="currentDeptId != null">current_dept_id = #{currentDeptId},</if>
            <if test="eventStatus != null">event_status = #{eventStatus},</if>
            <if test="flowStatus != null">flow_status = #{flowStatus},</if>
            <if test="relatedWorkOrderId != null">related_work_order_id = #{relatedWorkOrderId},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizEmergencyEventById" parameterType="Long">
        delete from biz_emergency_event where id = #{id}
    </delete>

    <delete id="deleteBizEmergencyEventByIds" parameterType="String">
        delete from biz_emergency_event where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 根据部门ID、事件状态、受理状态和流程状态统计数量 -->
    <select id="countByDeptIdAndFlowStatusIn" resultType="int">
        SELECT COUNT(1) 
        FROM biz_emergency_event
        WHERE current_dept_id = #{deptId}
        <if test="eventStatus != null">
            AND event_status = #{eventStatus}
        </if>
        <if test="acceptStatus != null">
            AND accept_status = #{acceptStatus}
        </if>
        <if test="flowStatuses != null and flowStatuses.length > 0">
            AND flow_status IN 
            <foreach item="status" collection="flowStatuses" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>
    
</mapper>
