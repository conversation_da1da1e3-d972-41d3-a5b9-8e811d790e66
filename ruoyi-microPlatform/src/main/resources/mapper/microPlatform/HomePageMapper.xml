<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.HomePageMapper">


    <select id="countComprehensiveEvents" resultType="int">
        SELECT COUNT(1)
        FROM biz_comprehensive_event
        WHERE 1 = 1
        <if test="param.startTimeBySql != null ">
            AND report_time &gt;= #{param.startTimeBySql}
        </if>
        <if test="param.endTimeBySql != null ">
            AND report_time &lt;= #{param.endTimeBySql}
        </if>
    </select>

    <select id="countMajorOpinionEvents" resultType="int">
        SELECT COUNT(1)
        FROM major_opinion_events
        WHERE 1 = 1
        <if test="param.startTimeBySql != null ">
            AND create_time &gt;= #{param.startTimeBySql}
        </if>
        <if test="param.endTimeBySql != null ">
            AND create_time &lt;= #{param.endTimeBySql}
        </if>
    </select>

    <select id="selectSuperviseStats" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT 
            'total' as lable,
            1 as type,
            COUNT(1) as count
        FROM 
            biz_comprehensive_event
        WHERE 
            1 = 1
            <if test="param.startTimeBySql != null ">
                AND report_time &gt;= #{param.startTimeBySql}
            </if>
            <if test="param.endTimeBySql != null ">
                AND report_time &lt;= #{param.endTimeBySql}
            </if>
        
        UNION ALL
        
        SELECT 
            'completed' as lable,
            2 as type,
            SUM(CASE WHEN status = '2' THEN 1 ELSE 0 END) as count
        FROM 
            biz_comprehensive_event
        WHERE 
            1 = 1
            <if test="param.startTimeBySql != null ">
                AND report_time &gt;= #{param.startTimeBySql}
            </if>
            <if test="param.endTimeBySql != null ">
                AND report_time &lt;= #{param.endTimeBySql}
            </if>
            
        UNION ALL
        
        SELECT 
            'satisfied' as lable,
            3 as type,
            SUM(CASE WHEN satisfaction = '1' THEN 1 ELSE 0 END) as count
        FROM 
            biz_comprehensive_event
        WHERE 
            1 = 1
            <if test="param.startTimeBySql != null ">
                AND report_time &gt;= #{param.startTimeBySql}
            </if>
            <if test="param.endTimeBySql != null ">
                AND report_time &lt;= #{param.endTimeBySql}
            </if>
    </select>

</mapper>
