<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.MajorOpinionEventMapper">
    
    <resultMap type="MajorOpinionEvent" id="MajorOpinionEventResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="eventStatus"    column="event_status"    />
        <result property="urgencyLevel"    column="urgency_level"    />
        <result property="severityLevel"    column="severity_level"    />
        <result property="eventTitle"    column="event_title"    />
        <result property="eventSummary"    column="event_summary"    />
        <result property="keywords"    column="keywords"    />
        <result property="involvedEntities"    column="involved_entities"    />
        <result property="geographicScope"    column="geographic_scope"    />
        <result property="eventTime"    column="event_time"    />
        <result property="accessory"    column="accessory"    />
        <result property="deptId"    column="dept_id"    />
        <result property="county"    column="county"    />
        <result property="country"    column="country"    />
        <result property="town"    column="town"    />
        <result property="gridId"    column="grid_id"    />
        <result property="gridName"    column="grid_name"    />
    </resultMap>

    <sql id="selectMajorOpinionEventVo">
        select id, event_id, event_status, urgency_level, severity_level, event_title, event_summary, 
               keywords, involved_entities, geographic_scope, event_time, create_by, create_time, 
               update_by, update_time, remark, accessory, dept_id, county, country, town, grid_id, grid_name
        from major_opinion_events
    </sql>

    <select id="selectMajorOpinionEventList" parameterType="MajorOpinionEvent" resultMap="MajorOpinionEventResult">
        <include refid="selectMajorOpinionEventVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="eventStatus != null "> and event_status = #{eventStatus}</if>
            <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="severityLevel != null  and severityLevel != ''"> and severity_level = #{severityLevel}</if>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="keywords != null  and keywords != ''"> and keywords like concat('%', #{keywords}, '%')</if>
            <if test="involvedEntities != null  and involvedEntities != ''"> and involved_entities like concat('%', #{involvedEntities}, '%')</if>
            <if test="geographicScope != null  and geographicScope != ''"> and geographic_scope = #{geographicScope}</if>
            <if test="eventTime != null "> and event_time = #{eventTime}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="county != null  and county != ''"> and county = #{county}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="town != null  and town != ''"> and town = #{town}</if>
            <if test="gridId != null "> and grid_id = #{gridId}</if>
            <if test="gridName != null  and gridName != ''"> and grid_name like concat('%', #{gridName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMajorOpinionEventById" parameterType="Long" resultMap="MajorOpinionEventResult">
        <include refid="selectMajorOpinionEventVo"/>
        where id = #{id}
    </select>
    
    <select id="selectLatestOpinionEvents" resultMap="MajorOpinionEventResult" parameterType="int">
        <include refid="selectMajorOpinionEventVo"/>
        order by event_time desc
        limit #{limit}
    </select>
        
    <select id="countByDeptId" resultType="int">
        SELECT COUNT(1) 
        FROM major_opinion_events
        WHERE dept_id = #{deptId}
        OR EXISTS (
            SELECT 1 FROM sys_dept t 
            WHERE find_in_set(#{deptId}, t.ancestors) 
            AND t.dept_id = major_opinion_events.dept_id
        )
    </select>
        
    <select id="selectOpinionCountByArea" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN county IS NOT NULL AND county != '' THEN county
                WHEN country IS NOT NULL AND country != '' THEN country
                WHEN town IS NOT NULL AND town != '' THEN town
                WHEN grid_name IS NOT NULL AND grid_name != '' THEN grid_name
                ELSE '其他'
            END AS area_name,
            COUNT(*) AS count
        FROM 
            major_opinion_events
        WHERE 
            (county IS NOT NULL AND county != '') OR 
            (country IS NOT NULL AND country != '') OR 
            (town IS NOT NULL AND town != '') OR 
            (grid_name IS NOT NULL AND grid_name != '')
        GROUP BY 
            CASE 
                WHEN county IS NOT NULL AND county != '' THEN county
                WHEN country IS NOT NULL AND country != '' THEN country
                WHEN town IS NOT NULL AND town != '' THEN town
                WHEN grid_name IS NOT NULL AND grid_name != '' THEN grid_name
                ELSE '其他'
            END
    </select>
        
    <!-- 按日期范围统计舆情事件数量 -->
    <select id="countByDateRange" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(event_time, '%Y-%m-%d') as date,
            COUNT(*) as count
        FROM 
            major_opinion_events
        WHERE 
            event_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY 
            DATE_FORMAT(event_time, '%Y-%m-%d')
        ORDER BY 
            date
    </select>

    <insert id="insertMajorOpinionEvent" parameterType="MajorOpinionEvent" useGeneratedKeys="true" keyProperty="id">
        insert into major_opinion_events
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id,</if>
            <if test="eventStatus != null">event_status,</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level,</if>
            <if test="severityLevel != null and severityLevel != ''">severity_level,</if>
            <if test="eventTitle != null and eventTitle != ''">event_title,</if>
            <if test="eventSummary != null">event_summary,</if>
            <if test="keywords != null">keywords,</if>
            <if test="involvedEntities != null">involved_entities,</if>
            <if test="geographicScope != null">geographic_scope,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="accessory != null">accessory,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">#{eventId},</if>
            <if test="eventStatus != null">#{eventStatus},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">#{urgencyLevel},</if>
            <if test="severityLevel != null and severityLevel != ''">#{severityLevel},</if>
            <if test="eventTitle != null and eventTitle != ''">#{eventTitle},</if>
            <if test="eventSummary != null">#{eventSummary},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="involvedEntities != null">#{involvedEntities},</if>
            <if test="geographicScope != null">#{geographicScope},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            sysdate(),
            sysdate()
         </trim>
    </insert>

    <update id="updateMajorOpinionEvent" parameterType="MajorOpinionEvent">
        update major_opinion_events
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id = #{eventId},</if>
            <if test="eventStatus != null">event_status = #{eventStatus},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level = #{urgencyLevel},</if>
            <if test="severityLevel != null and severityLevel != ''">severity_level = #{severityLevel},</if>
            <if test="eventTitle != null and eventTitle != ''">event_title = #{eventTitle},</if>
            <if test="eventSummary != null">event_summary = #{eventSummary},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="involvedEntities != null">involved_entities = #{involvedEntities},</if>
            <if test="geographicScope != null">geographic_scope = #{geographicScope},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMajorOpinionEventById" parameterType="Long">
        delete from major_opinion_events where id = #{id}
    </delete>

    <delete id="deleteMajorOpinionEventByIds" parameterType="String">
        delete from major_opinion_events where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
