<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizEventTypeMapper">
    
    <resultMap type="BizEventType" id="BizEventTypeResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="typeCode"    column="type_code"    />
        <result property="defaultTimeLimit"    column="default_time_limit"    />
        <result property="priorityRule"    column="priority_rule"    />
        <result property="defaultDept"    column="default_dept"    />
        <result property="processTemplate"    column="process_template"    />
        <result property="enabled"    column="enabled"    />
        <result property="level"    column="level"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBizEventTypeVo">
        select id, parent_id, type_name, type_code, default_time_limit, priority_rule, default_dept, process_template, enabled, level, sort, create_time, update_time from biz_event_type
    </sql>

    <select id="selectBizEventTypeList" parameterType="BizEventType" resultMap="BizEventTypeResult">
        <include refid="selectBizEventTypeVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="level != null "> and level = #{level}</if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectBizEventTypeById" parameterType="Long" resultMap="BizEventTypeResult">
        <include refid="selectBizEventTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBizEventType" parameterType="BizEventType" useGeneratedKeys="true" keyProperty="id">
        insert into biz_event_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="defaultTimeLimit != null">default_time_limit,</if>
            <if test="priorityRule != null">priority_rule,</if>
            <if test="defaultDept != null">default_dept,</if>
            <if test="processTemplate != null">process_template,</if>
            <if test="enabled != null">enabled,</if>
            <if test="level != null">level,</if>
            <if test="sort != null">sort,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="defaultTimeLimit != null">#{defaultTimeLimit},</if>
            <if test="priorityRule != null">#{priorityRule},</if>
            <if test="defaultDept != null">#{defaultDept},</if>
            <if test="processTemplate != null">#{processTemplate},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="level != null">#{level},</if>
            <if test="sort != null">#{sort},</if>
            sysdate(),
            sysdate()
         </trim>
    </insert>

    <update id="updateBizEventType" parameterType="BizEventType">
        update biz_event_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="defaultTimeLimit != null">default_time_limit = #{defaultTimeLimit},</if>
            <if test="priorityRule != null">priority_rule = #{priorityRule},</if>
            <if test="defaultDept != null">default_dept = #{defaultDept},</if>
            <if test="processTemplate != null">process_template = #{processTemplate},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="level != null">level = #{level},</if>
            <if test="sort != null">sort = #{sort},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizEventTypeById" parameterType="Long">
        delete from biz_event_type where id = #{id}
    </delete>

    <delete id="deleteBizEventTypeByIds" parameterType="String">
        delete from biz_event_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>
