<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizWorkOrderTaskMapper">
    
    <resultMap type="BizWorkOrderTask" id="BizWorkOrderTaskResult">
        <result property="id" column="id" />
        <result property="workOrderId" column="work_order_id" />
        <result property="assignDeptId" column="assign_dept_id" />
        <result property="assignDeptName" column="assign_dept_name" />
        <result property="assignDeptLevel" column="assign_dept_level" />
        <result property="flowDirection" column="flow_direction" />
        <result property="isDepartment" column="is_department" />
        <result property="handleDeptId" column="handle_dept_id" />
        <result property="handleDeptName" column="handle_dept_name" />
        <result property="handleDeptLevel" column="handle_dept_level" />
        <result property="handleType" column="handle_type" />
        <result property="hostTaskId" column="host_task_id" />
        <result property="taskContent" column="task_content" />
        <result property="taskStatus" column="task_status" />
        <result property="isWhistleblowing" column="is_whistleblowing" />
        <result property="whistleblowingTime" column="whistleblowing_time" />
        <result property="signTime" column="sign_time" />
        <result property="signUserId" column="sign_user_id" />
        <result property="signUserName" column="sign_user_name" />
        <result property="returnReason" column="return_reason" />
        <result property="returnTime" column="return_time" />
        <result property="transferDeptId" column="transfer_dept_id" />
        <result property="transferDeptName" column="transfer_dept_name" />
        <result property="transferDeptLevel" column="transfer_dept_level" />
        <result property="transferTime" column="transfer_time" />
        <result property="finishTime" column="finish_time" />
        <result property="sortNumber" column="sort_number" />
        <result property="isEnable" column="is_enable" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectBizWorkOrderTaskVo">
        select id, work_order_id, assign_dept_id, assign_dept_name, assign_dept_level, 
               flow_direction, is_department, handle_dept_id, handle_dept_name, handle_dept_level, 
               handle_type, host_task_id, task_content, task_status, is_whistleblowing, 
               whistleblowing_time, sign_time, sign_user_id, sign_user_name, return_reason, 
               return_time, transfer_dept_id, transfer_dept_name, transfer_dept_level, 
               transfer_time, finish_time, create_by, create_time, update_by, update_time, 
               sort_number, remark, is_enable 
        from biz_work_order_task
    </sql>

    <select id="selectBizWorkOrderTaskList" parameterType="BizWorkOrderTask" resultMap="BizWorkOrderTaskResult">
        <include refid="selectBizWorkOrderTaskVo"/>
        <where>  
            <if test="workOrderId != null "> and work_order_id = #{workOrderId}</if>
            <if test="assignDeptId != null "> and assign_dept_id = #{assignDeptId}</if>
            <if test="assignDeptName != null  and assignDeptName != ''"> and assign_dept_name like concat('%', #{assignDeptName}, '%')</if>
            <if test="assignDeptLevel != null "> and assign_dept_level = #{assignDeptLevel}</if>
            <if test="flowDirection != null "> and flow_direction = #{flowDirection}</if>
            <if test="isDepartment != null "> and is_department = #{isDepartment}</if>
            <if test="handleDeptId != null "> and handle_dept_id = #{handleDeptId}</if>
            <if test="handleDeptName != null  and handleDeptName != ''"> and handle_dept_name like concat('%', #{handleDeptName}, '%')</if>
            <if test="handleDeptLevel != null "> and handle_dept_level = #{handleDeptLevel}</if>
            <if test="handleType != null "> and handle_type = #{handleType}</if>
            <if test="hostTaskId != null "> and host_task_id = #{hostTaskId}</if>
            <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
            <if test="isWhistleblowing != null "> and is_whistleblowing = #{isWhistleblowing}</if>
            <if test="isEnable != null "> and is_enable = #{isEnable}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"> and create_time &gt;= #{params.beginCreateTime}</if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"> and create_time &lt;= #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectBizWorkOrderTaskById" parameterType="Long" resultMap="BizWorkOrderTaskResult">
        <include refid="selectBizWorkOrderTaskVo"/>
        where id = #{id}
    </select>
    
    <select id="countByHandleDeptIdAndTaskStatus" resultType="int">
        select count(1) 
        from biz_work_order_task
        where handle_dept_id = #{handleDeptId}
        <if test="taskStatus != null">
            and task_status = #{taskStatus}
        </if>
    </select>
        
    <insert id="insertBizWorkOrderTask" parameterType="BizWorkOrderTask" useGeneratedKeys="true" keyProperty="id">
        insert into biz_work_order_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="assignDeptId != null">assign_dept_id,</if>
            <if test="assignDeptName != null">assign_dept_name,</if>
            <if test="assignDeptLevel != null">assign_dept_level,</if>
            <if test="flowDirection != null">flow_direction,</if>
            <if test="isDepartment != null">is_department,</if>
            <if test="handleDeptId != null">handle_dept_id,</if>
            <if test="handleDeptName != null">handle_dept_name,</if>
            <if test="handleDeptLevel != null">handle_dept_level,</if>
            <if test="handleType != null">handle_type,</if>
            <if test="hostTaskId != null">host_task_id,</if>
            <if test="taskContent != null">task_content,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="isWhistleblowing != null">is_whistleblowing,</if>
            <if test="whistleblowingTime != null">whistleblowing_time,</if>
            <if test="signTime != null">sign_time,</if>
            <if test="signUserId != null">sign_user_id,</if>
            <if test="signUserName != null">sign_user_name,</if>
            <if test="returnReason != null">return_reason,</if>
            <if test="returnTime != null">return_time,</if>
            <if test="transferDeptId != null">transfer_dept_id,</if>
            <if test="transferDeptName != null">transfer_dept_name,</if>
            <if test="transferDeptLevel != null">transfer_dept_level,</if>
            <if test="transferTime != null">transfer_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="sortNumber != null">sort_number,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="assignDeptId != null">#{assignDeptId},</if>
            <if test="assignDeptName != null">#{assignDeptName},</if>
            <if test="assignDeptLevel != null">#{assignDeptLevel},</if>
            <if test="flowDirection != null">#{flowDirection},</if>
            <if test="isDepartment != null">#{isDepartment},</if>
            <if test="handleDeptId != null">#{handleDeptId},</if>
            <if test="handleDeptName != null">#{handleDeptName},</if>
            <if test="handleDeptLevel != null">#{handleDeptLevel},</if>
            <if test="handleType != null">#{handleType},</if>
            <if test="hostTaskId != null">#{hostTaskId},</if>
            <if test="taskContent != null">#{taskContent},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="isWhistleblowing != null">#{isWhistleblowing},</if>
            <if test="whistleblowingTime != null">#{whistleblowingTime},</if>
            <if test="signTime != null">#{signTime},</if>
            <if test="signUserId != null">#{signUserId},</if>
            <if test="signUserName != null">#{signUserName},</if>
            <if test="returnReason != null">#{returnReason},</if>
            <if test="returnTime != null">#{returnTime},</if>
            <if test="transferDeptId != null">#{transferDeptId},</if>
            <if test="transferDeptName != null">#{transferDeptName},</if>
            <if test="transferDeptLevel != null">#{transferDeptLevel},</if>
            <if test="transferTime != null">#{transferTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateBizWorkOrderTask" parameterType="BizWorkOrderTask">
        update biz_work_order_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="assignDeptId != null">assign_dept_id = #{assignDeptId},</if>
            <if test="assignDeptName != null">assign_dept_name = #{assignDeptName},</if>
            <if test="assignDeptLevel != null">assign_dept_level = #{assignDeptLevel},</if>
            <if test="flowDirection != null">flow_direction = #{flowDirection},</if>
            <if test="isDepartment != null">is_department = #{isDepartment},</if>
            <if test="handleDeptId != null">handle_dept_id = #{handleDeptId},</if>
            <if test="handleDeptName != null">handle_dept_name = #{handleDeptName},</if>
            <if test="handleDeptLevel != null">handle_dept_level = #{handleDeptLevel},</if>
            <if test="handleType != null">handle_type = #{handleType},</if>
            <if test="hostTaskId != null">host_task_id = #{hostTaskId},</if>
            <if test="taskContent != null">task_content = #{taskContent},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="isWhistleblowing != null">is_whistleblowing = #{isWhistleblowing},</if>
            <if test="whistleblowingTime != null">whistleblowing_time = #{whistleblowingTime},</if>
            <if test="signTime != null">sign_time = #{signTime},</if>
            <if test="signUserId != null">sign_user_id = #{signUserId},</if>
            <if test="signUserName != null">sign_user_name = #{signUserName},</if>
            <if test="returnReason != null">return_reason = #{returnReason},</if>
            <if test="returnTime != null">return_time = #{returnTime},</if>
            <if test="transferDeptId != null">transfer_dept_id = #{transferDeptId},</if>
            <if test="transferDeptName != null">transfer_dept_name = #{transferDeptName},</if>
            <if test="transferDeptLevel != null">transfer_dept_level = #{transferDeptLevel},</if>
            <if test="transferTime != null">transfer_time = #{transferTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="sortNumber != null">sort_number = #{sortNumber},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizWorkOrderTaskById" parameterType="Long">
        delete from biz_work_order_task where id = #{id}
    </delete>

    <delete id="deleteBizWorkOrderTaskByIds" parameterType="String">
        delete from biz_work_order_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 根据处理部门ID和状态数组统计数量 -->
    <select id="countByHandleDeptIdAndStatusIn" resultType="int">
        SELECT COUNT(1) 
        FROM biz_work_order_task
        WHERE handle_dept_id = #{deptId}
        <if test="statuses != null and statuses.length > 0">
            AND task_status IN 
            <foreach item="status" collection="statuses" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>
    
</mapper>
