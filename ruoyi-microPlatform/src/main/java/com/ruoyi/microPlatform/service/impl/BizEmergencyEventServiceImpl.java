package com.ruoyi.microPlatform.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.BizEmergencyEventMapper;
import com.ruoyi.microPlatform.domain.BizEmergencyEvent;
import com.ruoyi.microPlatform.service.IBizEmergencyEventService;

/**
 * 应急突发事件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Service
public class BizEmergencyEventServiceImpl implements IBizEmergencyEventService 
{
    @Autowired
    private BizEmergencyEventMapper bizEmergencyEventMapper;

    /**
     * 查询应急突发事件
     * 
     * @param id 应急突发事件主键
     * @return 应急突发事件
     */
    @Override
    public BizEmergencyEvent selectBizEmergencyEventById(Long id)
    {
        return bizEmergencyEventMapper.selectBizEmergencyEventById(id);
    }

    /**
     * 查询应急突发事件列表
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 应急突发事件
     */
    @Override
    public List<BizEmergencyEvent> selectBizEmergencyEventList(BizEmergencyEvent bizEmergencyEvent)
    {
        return bizEmergencyEventMapper.selectBizEmergencyEventList(bizEmergencyEvent);
    }

    /**
     * 新增应急突发事件
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 结果
     */
    @Override
    public int insertBizEmergencyEvent(BizEmergencyEvent bizEmergencyEvent)
    {
        return bizEmergencyEventMapper.insertBizEmergencyEvent(bizEmergencyEvent);
    }

    /**
     * 修改应急突发事件
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 结果
     */
    @Override
    public int updateBizEmergencyEvent(BizEmergencyEvent bizEmergencyEvent)
    {
        return bizEmergencyEventMapper.updateBizEmergencyEvent(bizEmergencyEvent);
    }

    /**
     * 批量删除应急突发事件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteBizEmergencyEventByIds(Long[] ids)
    {
        return bizEmergencyEventMapper.deleteBizEmergencyEventByIds(ids);
    }
    
    /**
     * 根据部门ID、事件状态、受理状态和流程状态统计数量
     *
     * @param deptId 部门ID
     * @param eventStatus 事件状态
     * @param acceptStatus 受理状态
     * @param flowStatuses 流程状态数组
     * @return 数量
     */
    @Override
    public int countByDeptIdAndFlowStatusIn(Long deptId, Integer eventStatus, Integer acceptStatus, Integer[] flowStatuses) {
        return bizEmergencyEventMapper.countByDeptIdAndFlowStatusIn(deptId, eventStatus, acceptStatus, flowStatuses);
    }

    /**
     * 删除应急突发事件信息
     * 
     * @param id 应急突发事件主键
     * @return 结果
     */
    @Override
    public int deleteBizEmergencyEventById(Long id)
    {
        return bizEmergencyEventMapper.deleteBizEmergencyEventById(id);
    }
    
    /**
     * 根据部门ID和状态统计数量
     * 
     * @param deptId 部门ID
     * @param eventStatus 事件状态
     * @param acceptStatus 签收状态
     * @return 数量
     */
    @Override
    public int countByDeptIdAndStatus(Long deptId, Integer eventStatus, Integer acceptStatus) 
    {
        return bizEmergencyEventMapper.countByDeptIdAndStatus(deptId, eventStatus, acceptStatus);
    }

}
