package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.BizDifficultIssue;
import com.ruoyi.microPlatform.domain.dto.DifficultIssueStatsDTO;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;

import java.util.List;
import java.util.Map;

/**
 * 疑难问题汇聚事项Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface IBizDifficultIssueService 
{
    /**
     * 查询疑难问题汇聚事项
     * 
     * @param id 疑难问题汇聚事项主键
     * @return 疑难问题汇聚事项
     */
    public BizDifficultIssue selectBizDifficultIssueById(Long id);

    /**
     * 查询疑难问题汇聚事项列表
     * 
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 疑难问题汇聚事项集合
     */
    public List<BizDifficultIssue> selectBizDifficultIssueList(BizDifficultIssue bizDifficultIssue);

    /**
     * 新增疑难问题汇聚事项
     * 
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 结果
     */
    public int insertBizDifficultIssue(BizDifficultIssue bizDifficultIssue);

    /**
     * 修改疑难问题汇聚事项
     * 
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 结果
     */
    public int updateBizDifficultIssue(BizDifficultIssue bizDifficultIssue);

    /**
     * 批量删除疑难问题汇聚事项
     * 
     * @param ids 需要删除的疑难问题汇聚事项主键集合
     * @return 结果
     */
    public int deleteBizDifficultIssueByIds(Long[] ids);

    /**
     * 根据状态统计数量
     * 
     * @param status 状态
     * @return 数量
     */
    public int countByStatus(Integer status);

    /**
     * 删除疑难问题汇聚事项信息
     * 
     * @param id 疑难问题汇聚事项主键
     * @return 结果
     */
    public int deleteBizDifficultIssueById(Long id);

}
