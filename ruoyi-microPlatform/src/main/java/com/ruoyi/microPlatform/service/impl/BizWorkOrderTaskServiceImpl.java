package com.ruoyi.microPlatform.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.BizWorkOrderTaskMapper;
import com.ruoyi.microPlatform.domain.BizWorkOrderTask;
import com.ruoyi.microPlatform.service.IBizWorkOrderTaskService;

/**
 * 主协办任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Service
public class BizWorkOrderTaskServiceImpl implements IBizWorkOrderTaskService 
{
    @Autowired
    private BizWorkOrderTaskMapper bizWorkOrderTaskMapper;

    /**
     * 查询主协办任务
     * 
     * @param id 主协办任务主键
     * @return 主协办任务
     */
    @Override
    public BizWorkOrderTask selectBizWorkOrderTaskById(Long id)
    {
        return bizWorkOrderTaskMapper.selectBizWorkOrderTaskById(id);
    }

    /**
     * 查询主协办任务列表
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 主协办任务
     */
    @Override
    public List<BizWorkOrderTask> selectBizWorkOrderTaskList(BizWorkOrderTask bizWorkOrderTask)
    {
        return bizWorkOrderTaskMapper.selectBizWorkOrderTaskList(bizWorkOrderTask);
    }

    /**
     * 新增主协办任务
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 结果
     */
    @Override
    public int insertBizWorkOrderTask(BizWorkOrderTask bizWorkOrderTask)
    {
        return bizWorkOrderTaskMapper.insertBizWorkOrderTask(bizWorkOrderTask);
    }

    /**
     * 修改主协办任务
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 结果
     */
    @Override
    public int updateBizWorkOrderTask(BizWorkOrderTask bizWorkOrderTask)
    {
        return bizWorkOrderTaskMapper.updateBizWorkOrderTask(bizWorkOrderTask);
    }

    /**
     * 批量删除主协办任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteBizWorkOrderTaskByIds(Long[] ids)
    {
        return bizWorkOrderTaskMapper.deleteBizWorkOrderTaskByIds(ids);
    }
    
    /**
     * 根据处理部门ID和状态统计数量
     * 
     * @param deptId 部门ID
     * @param statuses 状态数组
     * @return 数量
     */
    @Override
    public int countByHandleDeptIdAndStatusIn(Long deptId, Integer[] statuses) {
        return bizWorkOrderTaskMapper.countByHandleDeptIdAndStatusIn(deptId, statuses);
    }

    /**
     * 删除主协办任务信息
     * 
     * @param id 主协办任务主键
     * @return 结果
     */
    @Override
    public int deleteBizWorkOrderTaskById(Long id)
    {
        return bizWorkOrderTaskMapper.deleteBizWorkOrderTaskById(id);
    }

    /**
     * 根据部门ID和任务状态统计数量
     * 
     * @param handleDeptId 处理部门ID
     * @param taskStatus 任务状态
     * @return 数量
     */
    @Override
    public int countByHandleDeptIdAndTaskStatus(Long handleDeptId, Integer taskStatus) 
    {
        return bizWorkOrderTaskMapper.countByHandleDeptIdAndTaskStatus(handleDeptId, taskStatus);
    }
}
