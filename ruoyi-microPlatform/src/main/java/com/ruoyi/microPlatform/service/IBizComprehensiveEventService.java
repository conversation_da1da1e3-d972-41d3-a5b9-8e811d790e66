package com.ruoyi.microPlatform.service;


import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.EventTrendVO;
import com.ruoyi.microPlatform.domain.BizComprehensiveEvent;
import com.ruoyi.microPlatform.domain.dto.DifficultIssueStatsDTO;

import java.util.List;
import java.util.Map;

/**
 * 综合事项Service接口
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface IBizComprehensiveEventService {
    /**
     * 查询综合事项
     *
     * @param id 综合事项主键
     * @return 综合事项
     */
    public BizComprehensiveEvent selectBizComprehensiveEventById(Long id);

    /**
     * 查询综合事项列表
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 综合事项集合
     */
    public List<BizComprehensiveEvent> selectBizComprehensiveEventList(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 新增综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    public int insertBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 修改综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    public int updateBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 批量删除综合事项
     *
     * @param ids 需要删除的综合事项主键集合
     * @return 结果
     */
    public int deleteBizComprehensiveEventByIds(Long[] ids);

    /**
     * 删除综合事项信息
     *
     * @param id 综合事项主键
     * @return 结果
     */
    public int deleteBizComprehensiveEventById(Long id);

    /**
     * 统计所有综合事项数量
     * 
     * @return 综合事项总数
     */
    public int countAll();

    // In IBizComprehensiveEventService.java

    /**
     * 获取办件数量趋势数据
     *
     * @return 办件趋势数据
     */
    EventTrendVO getBizComprehensiveEventTrend();

    /**
     * 获取热门事项TOP6
     * @param param 查询参数，包含时间维度信息
     * @return 热门事项列表，按数量降序排序，最多返回6条
     */
    List<Map<String, Object>> getTop6EventMajors(BigdataParam param);


    /**
     * 获取疑难问题统计信息（按来源系统分组）
     *
     * @param bigdataParam 查询参数
     * @return 统计结果
     */
    public DifficultIssueStatsDTO getDifficultIssueStats(BigdataParam bigdataParam);
}