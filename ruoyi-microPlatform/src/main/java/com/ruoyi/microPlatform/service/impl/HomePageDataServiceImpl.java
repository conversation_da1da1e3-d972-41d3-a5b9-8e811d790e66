package com.ruoyi.microPlatform.service.impl;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.controller.bigdata.gov.GovBiz;
import com.ruoyi.microPlatform.controller.bigdata.gov.req.GovGetUnifiedManagementLetterListReq;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetUnifiedManagementLetterListResp;
import com.ruoyi.microPlatform.mapper.HomePageMapper;
import com.ruoyi.microPlatform.mapper.SlaveHomePageMapper;
import com.ruoyi.microPlatform.service.HomePageDataService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class HomePageDataServiceImpl implements HomePageDataService {
    @Resource
    private HomePageMapper homePageMapper;
    @Resource
    private SlaveHomePageMapper slaveHomePageMapper;
    @Resource
    private GovBiz govBiz;
    @Resource
    private RedisCache redisCache;

    @Scheduled(cron = "0 0 0/1 * * ? ")
    public void refreshData() {
        getGovTotal(0, true);
    }

    public Integer getGovTotal(Integer dimension, boolean refresh) {
        if (dimension == null) {
            dimension = 1;
        }
        GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
        // 今天
        DateTime date = DateUtil.date();
        Integer total = 0;
        if (refresh) {
            if (dimension == 1) {
                govGetUnifiedManagementLetterListReq.setBegindate(DateUtil.format(DateUtil.beginOfDay(date), "yyyy-MM-dd HH:mm:ss"));
                govGetUnifiedManagementLetterListReq.setEnddate(DateUtil.format(DateUtil.endOfDay(date), "yyyy-MM-dd HH:mm:ss"));
                GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
                redisCache.setCacheObject("BIGDATA:HomePageData:GOV:DAY", unifiedManagementLetterList.getTotal() + "", 60, TimeUnit.MINUTES);
                total = unifiedManagementLetterList.getTotal();
            } else if (dimension == 2) {
                // 本周
                govGetUnifiedManagementLetterListReq.setBegindate(DateUtil.format(DateUtil.beginOfWeek(date), "yyyy-MM-dd HH:mm:ss"));
                govGetUnifiedManagementLetterListReq.setEnddate(DateUtil.format(DateUtil.endOfWeek(date), "yyyy-MM-dd HH:mm:ss"));
                GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
                redisCache.setCacheObject("BIGDATA:HomePageData:GOV:WEEK", unifiedManagementLetterList.getTotal() + "", 60, TimeUnit.MINUTES);
                total = unifiedManagementLetterList.getTotal();
            } else if (dimension == 3) {
                // 本月
                govGetUnifiedManagementLetterListReq.setBegindate(DateUtil.format(DateUtil.beginOfMonth(date), "yyyy-MM-dd HH:mm:ss"));
                govGetUnifiedManagementLetterListReq.setEnddate(DateUtil.format(DateUtil.endOfMonth(date), "yyyy-MM-dd HH:mm:ss"));
                GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
                redisCache.setCacheObject("BIGDATA:HomePageData:GOV:MONTH", unifiedManagementLetterList.getTotal() + "", 60, TimeUnit.MINUTES);
                total = unifiedManagementLetterList.getTotal();
            } else if (dimension == 4) {
                // 本年
                govGetUnifiedManagementLetterListReq.setBegindate(DateUtil.format(DateUtil.beginOfYear(date), "yyyy-MM-dd HH:mm:ss"));
                govGetUnifiedManagementLetterListReq.setEnddate(DateUtil.format(DateUtil.endOfYear(date), "yyyy-MM-dd HH:mm:ss"));
                GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
                redisCache.setCacheObject("BIGDATA:HomePageData:GOV:YEAR", unifiedManagementLetterList.getTotal() + "", 60, TimeUnit.MINUTES);
                total = unifiedManagementLetterList.getTotal();
            }
        } else {
            if (dimension == 1) {
                total = redisCache.getCacheObject("BIGDATA:HomePageData:GOV:DAY");
            } else if (dimension == 2) {
                // 本周
                total = redisCache.getCacheObject("BIGDATA:HomePageData:GOV:WEEK");
            } else if (dimension == 3) {
                // 本月
                total = redisCache.getCacheObject("BIGDATA:HomePageData:GOV:MONTH");
            } else if (dimension == 4) {
                // 本年
                total = redisCache.getCacheObject("BIGDATA:HomePageData:GOV:YEAR");
            }
        }
        if (total == null) {
            total = 0;
        }

        return total;


    }


    @Override
    public Map<String, Integer> getIssueCounts(BigdataParam param) {
        // Calculate date range based on dimension
        param.calculateDateRange();

        Map<String, Integer> counts = new LinkedHashMap<>();

        // 1. 12345平台
        counts.put("hotline", slaveHomePageMapper.countHotlineDetails(param));

        // 2. 运管服
        counts.put("umService", slaveHomePageMapper.countUmEvents(param));

        // 3. 综治
        counts.put("comprehensive", homePageMapper.countComprehensiveEvents(param));

        // 4. 许邻e家
        counts.put("ehome", 0);

        //市长信箱 要走 api

        // 5. 其他 (万人助企 + 应急局事项 + 市长信箱 + 信访 + 舆情事项)
        int others = slaveHomePageMapper.countEnterpriseAppeals(param) +
                slaveHomePageMapper.countEmergencyEvents(param) +
                getGovTotal(param.getDimension(), false) +
                slaveHomePageMapper.countPetitionInfos(param) +
                homePageMapper.countMajorOpinionEvents(param);

        counts.put("others", others);

        return counts;
    }

    @Override
    public Map<String, Integer> getSuperviseCounts(BigdataParam param) {
        param.calculateDateRange();


        Map<String, Integer> result = new LinkedHashMap<>();
        result.put("totalCount", totalCount);      // 累计办理事项
        result.put("completionRate", completionRate);  // 办结率 (%)
        result.put("satisfactionRate", satisfactionRate); // 满意率 (%)

        return result;
    }
}
