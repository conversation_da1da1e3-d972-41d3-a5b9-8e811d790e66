package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.BizEventType;

/**
 * 问题类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
public interface IBizEventTypeService 
{
    /**
     * 查询问题类型
     * 
     * @param id 问题类型主键
     * @return 问题类型
     */
    public BizEventType selectBizEventTypeById(Long id);

    /**
     * 查询问题类型列表
     * 
     * @param bizEventType 问题类型
     * @return 问题类型集合
     */
    public List<BizEventType> selectBizEventTypeList(BizEventType bizEventType);

    /**
     * 新增问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    public int insertBizEventType(BizEventType bizEventType);

    /**
     * 修改问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    public int updateBizEventType(BizEventType bizEventType);

    /**
     * 批量删除问题类型
     * 
     * @param ids 需要删除的问题类型主键集合
     * @return 结果
     */
    public int deleteBizEventTypeByIds(Long[] ids);

    /**
     * 删除问题类型信息
     * 
     * @param id 问题类型主键
     * @return 结果
     */
    public int deleteBizEventTypeById(Long id);
}
