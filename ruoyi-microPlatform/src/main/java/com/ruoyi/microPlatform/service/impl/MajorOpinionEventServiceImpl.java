package com.ruoyi.microPlatform.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.system.service.ISysDeptService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.MajorOpinionEventMapper;
import com.ruoyi.microPlatform.domain.MajorOpinionEvent;
import com.ruoyi.microPlatform.service.IMajorOpinionEventService;

/**
 * 重大舆情信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Service
public class MajorOpinionEventServiceImpl implements IMajorOpinionEventService {
    @Autowired
    private MajorOpinionEventMapper majorOpinionEventMapper;

    /**
     * 查询重大舆情信息
     *
     * @param id 重大舆情信息主键
     * @return 重大舆情信息
     */
    @Override
    public MajorOpinionEvent selectMajorOpinionEventById(Long id) {
        return majorOpinionEventMapper.selectMajorOpinionEventById(id);
    }

    /**
     * 查询重大舆情信息列表
     *
     * @param majorOpinionEvent 重大舆情信息
     * @return 重大舆情信息
     */
    @Override
    public List<MajorOpinionEvent> selectMajorOpinionEventList(MajorOpinionEvent majorOpinionEvent) {
        return majorOpinionEventMapper.selectMajorOpinionEventList(majorOpinionEvent);
    }

    /**
     * 新增重大舆情信息
     *
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    @Override
    public int insertMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent) {
        return majorOpinionEventMapper.insertMajorOpinionEvent(majorOpinionEvent);
    }

    /**
     * 修改重大舆情信息
     *
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    @Override
    public int updateMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent) {
        return majorOpinionEventMapper.updateMajorOpinionEvent(majorOpinionEvent);
    }

    /**
     * 批量删除重大舆情信息
     *
     * @param ids 需要删除的重大舆情信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorOpinionEventByIds(Long[] ids) {
        return majorOpinionEventMapper.deleteMajorOpinionEventByIds(ids);
    }

    /**
     * 按日期范围统计舆情事件数量
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 包含日期和数量的Map列表
     */
    @Override
    public List<Map<String, Object>> countByDateRange(Date startDate, Date endDate) {
        return majorOpinionEventMapper.countByDateRange(startDate, endDate);
    }

    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取每个区域的舆情数量
     *
     * @return 区域舆情数量列表
     */
    @Override
    public List<CommonBaseCount> letterArea() {
        // 获取所有一级部门
        SysDept deptQuery = new SysDept();
        deptQuery.setParentId(1L); // 假设1是根部门ID
        List<SysDept> deptList = deptService.selectDeptList(deptQuery);

        if (CollectionUtil.isEmpty(deptList)) {
            return new ArrayList<>();
        }

        List<CommonBaseCount> result = new ArrayList<>();
        for (SysDept dept : deptList) {
            int count = majorOpinionEventMapper.countByDeptId(dept.getDeptId());
            CommonBaseCount countObj = new CommonBaseCount();
            countObj.setLable(dept.getDeptName());
            countObj.setCount(count);
            result.add(countObj);
        }

        return result;
    }

    /**
     * 删除重大舆情信息信息
     *
     * @param id 重大舆情信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorOpinionEventById(Long id) {
        return majorOpinionEventMapper.deleteMajorOpinionEventById(id);
    }

    /**
     * 获取最新的舆情事项
     *
     * @param limit 限制数量
     * @return 舆情事项列表
     */
    @Override
    public List<MajorOpinionEvent> selectLatestOpinionEvents(int limit) {
        return majorOpinionEventMapper.selectLatestOpinionEvents(limit);
    }
}
