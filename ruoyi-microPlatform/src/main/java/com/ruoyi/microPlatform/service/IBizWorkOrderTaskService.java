package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.BizWorkOrderTask;

/**
 * 主协办任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
public interface IBizWorkOrderTaskService 
{
    /**
     * 查询主协办任务
     * 
     * @param id 主协办任务主键
     * @return 主协办任务
     */
    public BizWorkOrderTask selectBizWorkOrderTaskById(Long id);

    /**
     * 查询主协办任务列表
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 主协办任务集合
     */
    public List<BizWorkOrderTask> selectBizWorkOrderTaskList(BizWorkOrderTask bizWorkOrderTask);

    /**
     * 新增主协办任务
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 结果
     */
    public int insertBizWorkOrderTask(BizWorkOrderTask bizWorkOrderTask);

    /**
     * 修改主协办任务
     * 
     * @param bizWorkOrderTask 主协办任务
     * @return 结果
     */
    public int updateBizWorkOrderTask(BizWorkOrderTask bizWorkOrderTask);

    /**
     * 批量删除主协办任务
     * 
     * @param ids 需要删除的主协办任务主键集合
     * @return 结果
     */
    public int deleteBizWorkOrderTaskByIds(Long[] ids);

    /**
     * 删除主协办任务信息
     * 
     * @param id 主协办任务主键
     * @return 结果
     */
    public int deleteBizWorkOrderTaskById(Long id);

    /**
     * 根据处理部门ID和状态统计数量
     * 
     * @param deptId 部门ID
     * @param statuses 状态数组
     * @return 数量
     */
    public int countByHandleDeptIdAndStatusIn(Long deptId, Integer[] statuses);

    /**
     * 根据部门ID和任务状态统计数量
     * 
     * @param handleDeptId 处理部门ID
     * @param taskStatus 任务状态
     * @return 数量
     */
    public int countByHandleDeptIdAndTaskStatus(Long handleDeptId, Integer taskStatus);
}
