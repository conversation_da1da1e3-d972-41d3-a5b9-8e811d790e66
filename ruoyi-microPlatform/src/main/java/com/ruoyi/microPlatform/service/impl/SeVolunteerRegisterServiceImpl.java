package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;
import com.ruoyi.microPlatform.mapper.SeVolunteerRegisterMapper;
import com.ruoyi.microPlatform.service.ISeVolunteerRegisterService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 志愿报名Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeVolunteerRegisterServiceImpl implements ISeVolunteerRegisterService {
    @Autowired
    private SeVolunteerRegisterMapper seVolunteerRegisterMapper;

    @Autowired
    private WeChatService weChatService;

    /**
     * 查询志愿报名
     *
     * @param id 志愿报名主键
     * @return 志愿报名
     */
    @Override
    public SeVolunteerRegister selectSeVolunteerRegisterById(Long id) {
        return seVolunteerRegisterMapper.selectSeVolunteerRegisterById(id);
    }

    /**
     * 校验志愿报名是否存在
     *
     * @param seVolunteerRegister
     * @return boolean
     */
    @Override
    public boolean checkSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister) {
        SeVolunteerRegister old = seVolunteerRegisterMapper.checkSeVolunteerRegisterUnique(seVolunteerRegister);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询志愿报名列表
     *
     * @param seVolunteerRegister 志愿报名
     * @return 志愿报名
     */
    @Override
    public List<SeVolunteerRegister> selectSeVolunteerRegisterList(SeVolunteerRegister seVolunteerRegister) {
        return seVolunteerRegisterMapper.selectSeVolunteerRegisterList(seVolunteerRegister);
    }

    /**
     * 导入志愿报名
     *
     * @param infos           志愿报名列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeVolunteerRegister(List<SeVolunteerRegister> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入志愿报名数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeVolunteerRegister info : infos) {
            try {
                // 验证是否存在这个数据
                SeVolunteerRegister old = seVolunteerRegisterMapper.checkSeVolunteerRegisterUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seVolunteerRegisterMapper.insertSeVolunteerRegister(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seVolunteerRegisterMapper.updateSeVolunteerRegister(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增志愿报名
     *
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    @Override
    public int insertSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister) {
        seVolunteerRegister.setCreateTime(DateUtils.getNowDate());
        int i = seVolunteerRegisterMapper.insertSeVolunteerRegister(seVolunteerRegister);
        if (i > 0) {
            //TODO: 给社区人员发送消息通知
            weChatService.insertMsg(seVolunteerRegister.getDeptId(), "志愿报名",
                    "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/volunteer/detail?ids=" + seVolunteerRegister.getId(),
                    seVolunteerRegister.getTown(), "居民提交了一条新的志愿报名记录");
        }
        return i;
    }

    /**
     * 修改志愿报名
     *
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    @Override
    public int updateSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister) {
        return seVolunteerRegisterMapper.updateSeVolunteerRegister(seVolunteerRegister);
    }

    /**
     * 批量删除志愿报名
     *
     * @param ids 需要删除的志愿报名主键
     * @return 结果
     */
    @Override
    public int deleteSeVolunteerRegisterByIds(Long[] ids) {
        return seVolunteerRegisterMapper.deleteSeVolunteerRegisterByIds(ids);
    }

    /**
     * 删除志愿报名信息
     *
     * @param id 志愿报名主键
     * @return 结果
     */
    @Override
    public int deleteSeVolunteerRegisterById(Long id) {
        return seVolunteerRegisterMapper.deleteSeVolunteerRegisterById(id);
    }

    @Override
    public Map<String, Object> selectSubmitCount(SeVolunteerRegister seVolunteerRegister) {
        return seVolunteerRegisterMapper.selectSubmitCount(seVolunteerRegister);
    }

    @Override
    public int selectCount(BigdataParam bigdataParam) {
        return seVolunteerRegisterMapper.selectCountByBigData(bigdataParam);
    }


    @Resource
    private ISysDeptService deptService;

    @Override
    public List<CommonBaseCount> iSeVolunteerRegisterCountByDept(BigdataParam bigdataParam) {
        if (bigdataParam.getDeptId() == null) {
            throw new RuntimeException("区域错误！");
        }

        // 查询子部门
        SysDept query = new SysDept();
        query.setParentId(bigdataParam.getDeptId());
        List<SysDept> sysDepts = deptService.selectDeptList(query);
        if (CollectionUtil.isEmpty(sysDepts)) {
            return new ArrayList<>();
        }

        List<CommonBaseCount> res = new ArrayList<>();

        // 统计每个部门的志愿者数量
        for (SysDept e : sysDepts) {
            HomePage homePage = new HomePage();
            homePage.setDeptId(e.getDeptId());
            int volunteerCount = seVolunteerRegisterMapper.selectSubmitCountByHome(homePage);
            if (volunteerCount > 0) {  // 只保留有数据的
                CommonBaseCount count = new CommonBaseCount();
                count.setLable(e.getDeptName());
                count.setCount(volunteerCount);
                res.add(count);
            }
        }

        // 如果有效数据少于6个，补充0
        if (res.size() < 6) {
            for (SysDept e : sysDepts) {
                // 已存在的部门不要重复补
                boolean exists = res.stream().anyMatch(c -> c.getLable().equals(e.getDeptName()));
                if (!exists) {
                    CommonBaseCount count = new CommonBaseCount();
                    count.setLable(e.getDeptName());
                    count.setCount(0);
                    res.add(count);
                }
                if (res.size() >= 6) {
                    break;
                }
            }
        }

        return res;
    }

}
