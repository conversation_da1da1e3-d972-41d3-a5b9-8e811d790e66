package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;
import com.ruoyi.microPlatform.domain.TbSidelineGridUser;

/**
 * 兼职网格员信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ITbSidelineGridUserService 
{
    /**
     * 查询兼职网格员信息
     * 
     * @param id 兼职网格员信息主键
     * @return 兼职网格员信息
     */
    public TbSidelineGridUser selectTbSidelineGridUserById(Long id);

    /**
     * 校验兼职网格员信息是否存在
     *
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 兼职网格员信息
     */
    public boolean checkTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 查询兼职网格员信息列表
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 兼职网格员信息集合
     */
    public List<TbSidelineGridUser> selectTbSidelineGridUserList(TbSidelineGridUser tbSidelineGridUser);

    public Integer selectTbSidelineGridUserCount(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 导入兼职网格员信息
     *
     * @param infos       兼职网格员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSidelineGridUser(List<TbSidelineGridUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    public int insertTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser) ;

    /**
     * 修改兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    public int updateTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 批量删除兼职网格员信息
     * 
     * @param ids 需要删除的兼职网格员信息主键集合
     * @return 结果
     */
    public int deleteTbSidelineGridUserByIds(Long[] ids);

    /**
     * 删除兼职网格员信息信息
     * 
     * @param tbSidelineGridInfo 兼职网格员信息
     * @return 结果
     */
    public int deleteTbSidelineGridUserById(TbSidelineGridInfo tbSidelineGridInfo);

    List<TbGridInfo> selectTbSidelineGridUserlistByGrid(TbSidelineGridUser tbSidelineGridUser,List<TbGridInfo> gridInfos);

}
