package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.EventTrendVO;
import com.ruoyi.microPlatform.domain.BizComprehensiveEvent;
import com.ruoyi.microPlatform.domain.BizEventType;
import com.ruoyi.microPlatform.domain.dto.DifficultIssueStatsDTO;
import com.ruoyi.microPlatform.mapper.BizComprehensiveEventMapper;
import com.ruoyi.microPlatform.service.IBizComprehensiveEventService;
import com.ruoyi.microPlatform.service.IBizEventTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 综合事项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
public class BizComprehensiveEventServiceImpl implements IBizComprehensiveEventService {
    @Autowired
    private BizComprehensiveEventMapper bizComprehensiveEventMapper;

    /**
     * 查询综合事项
     *
     * @param id 综合事项主键
     * @return 综合事项
     */
    @Override
    public BizComprehensiveEvent selectBizComprehensiveEventById(Long id) {
        return bizComprehensiveEventMapper.selectBizComprehensiveEventById(id);
    }

    /**
     * 查询综合事项列表
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 综合事项
     */
    @Override
    public List<BizComprehensiveEvent> selectBizComprehensiveEventList(BizComprehensiveEvent bizComprehensiveEvent) {
        return bizComprehensiveEventMapper.selectBizComprehensiveEventList(bizComprehensiveEvent);
    }

    /**
     * 新增综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    @Override
    public int insertBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent) {
        return bizComprehensiveEventMapper.insertBizComprehensiveEvent(bizComprehensiveEvent);
    }

    /**
     * 修改综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    @Override
    public int updateBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent) {
        return bizComprehensiveEventMapper.updateBizComprehensiveEvent(bizComprehensiveEvent);
    }

    /**
     * 批量删除综合事项
     *
     * @param ids 需要删除的综合事项主键
     * @return 结果
     */
    @Override
    public int deleteBizComprehensiveEventByIds(Long[] ids) {
        return bizComprehensiveEventMapper.deleteBizComprehensiveEventByIds(ids);
    }

    /**
     * 统计所有综合事项数量
     * 
     * @return 综合事项总数
     */
    @Override
    public int countAll() {
        return bizComprehensiveEventMapper.countAll();
    }

    /**
     * 删除综合事项信息
     *
     * @param id 综合事项主键
     * @return 结果
     */
    @Override
    public int deleteBizComprehensiveEventById(Long id) {
        return bizComprehensiveEventMapper.deleteBizComprehensiveEventById(id);
    }

    @Resource
    private IBizEventTypeService bizEventTypeService;

    // In BizComprehensiveEventServiceImpl.java
    @Override
    public List<Map<String, Object>> getTop6EventMajors(BigdataParam param) {
        // 获取所有大类事件类型
        BizEventType query = new BizEventType();
        query.setParentId(0L);
        List<BizEventType> allMajorTypes = bizEventTypeService.selectBizEventTypeList(query);
        
        // 获取统计结果
        List<Map<String, Object>> result = bizComprehensiveEventMapper.selectTop6EventMajors(param);
        
        // 如果结果为空，直接返回所有大类，数量设为0
        if (CollectionUtils.isEmpty(result)) {
            return allMajorTypes.stream()
                .limit(6) // 限制最多返回6条
                .map(type -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("typeName", type.getTypeName());
                    map.put("count", 0);
                    return map;
                })
                .collect(Collectors.toList());
        }
        
        // 将结果转换为以typeName为key的Map
        Map<String, Map<String, Object>> resultMap = result.stream()
            .collect(Collectors.toMap(
                map -> String.valueOf(map.get("typeName")),
                map -> map,
                (existing, replacement) -> existing
            ));
        
        // 合并结果，确保所有大类都包含在返回结果中
        return allMajorTypes.stream()
            .limit(6) // 限制最多返回6条
            .map(type -> {
                Map<String, Object> map = resultMap.getOrDefault(type.getTypeName(), new HashMap<>());
                // 如果该类型在结果中不存在，则创建新条目
                if (map.isEmpty()) {
                    map.put("typeName", type.getTypeName());
                    map.put("count", 0);
                }
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public EventTrendVO getBizComprehensiveEventTrend() {
        // 这个地方没找到字典 没办法在没数据的时候补充
        // 1. 初始化返回对象
        EventTrendVO result = new EventTrendVO();

        // 2. 获取最近7天的日期列表
        List<String> dateList = new ArrayList<>();
        List<Integer> eventCountList = new ArrayList<>();
        List<Integer> difficultEventCountList = new ArrayList<>();

        // 3. 获取日期范围
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6); // 最近7天

        // 4. 查询数据库获取数据
        List<Map<String, Object>> eventTrends = bizComprehensiveEventMapper.selectEventTrendByDateRange(
                startDate.atStartOfDay(),
                endDate.plusDays(1).atStartOfDay()
        );

        // 5. 将查询结果转为按日期分组的Map
        Map<String, Map<String, Integer>> trendMap = eventTrends.stream()
                .collect(Collectors.groupingBy(
                        m -> (String) m.get("eventDate"),
                        Collectors.toMap(
                                m -> (String) m.get("eventType"),
                                m -> ((Number) m.get("count")).intValue()
                        )
                ));

        // 6. 填充7天数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            String dateStr = date.format(formatter);
            dateList.add(dateStr);

            // 获取当天的数据
            Map<String, Integer> dateData = trendMap.get(dateStr);

            // 普通办件数量
            eventCountList.add(dateData != null ?
                    dateData.getOrDefault("normal", 0) + dateData.getOrDefault("difficult", 0) : 0);

            // 疑难办件数量
            difficultEventCountList.add(dateData != null ?
                    dateData.getOrDefault("difficult", 0) : 0);
        }

        // 7. 设置返回结果
        result.setDateList(dateList);
        result.setEventCountList(eventCountList);
        result.setDifficultEventCountList(difficultEventCountList);

        return result;
    }

    /**
     * 获取疑难问题汇聚事项统计信息
     *
     * @param bigdataParam 统计参数
     * @return 统计信息
     */
    @Override
    public DifficultIssueStatsDTO getDifficultIssueStats(BigdataParam bigdataParam) {
        DifficultIssueStatsDTO statsDTO = new DifficultIssueStatsDTO();
        bigdataParam.calculateDateRange();

        // 获取总数
        int totalCount = bizComprehensiveEventMapper.countDifficultIssues(bigdataParam);
        statsDTO.setTotalCount(totalCount);

        // 获取按来源系统分组的统计
        List<Map<String, Object>> sourceSystemStats = bizComprehensiveEventMapper.countBySourceSystem(bigdataParam);
        if (!CollectionUtils.isEmpty(sourceSystemStats)) {
            Map<String, Integer> statsMap = new HashMap<>();
            for (Map<String, Object> stat : sourceSystemStats) {
                String sourceSystem = (String) stat.get("sourceSystem");
                Long count = (Long) stat.get("count");
                if (sourceSystem != null && count != null) {
                    statsMap.put(sourceSystem, count.intValue());
                }
            }
            statsDTO.setStatsBySourceSystem(statsMap);
        }else {
            statsDTO.setStatsBySourceSystem(new HashMap<>());
        }

        return statsDTO;
    }
}
