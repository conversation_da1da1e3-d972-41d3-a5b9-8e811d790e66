package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyEvaluationResult;
import com.ruoyi.microPlatform.mapper.PbPartyEvaluationResultMapper;
import com.ruoyi.microPlatform.service.IPbPartyEvaluationResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Objects;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 党建评价结果Service业务层处理
 */
@Service
public class PbPartyEvaluationResultServiceImpl implements IPbPartyEvaluationResultService {

    @Autowired
    private PbPartyEvaluationResultMapper mapper;

    @Override
    public PbPartyEvaluationResult selectPbPartyEvaluationResultById(Long id) {
        return mapper.selectPbPartyEvaluationResultById(id);
    }

    @Override
    public boolean checkPbPartyEvaluationResult(PbPartyEvaluationResult info) {
        return mapper.checkPbPartyEvaluationResultUnique(info) != null;
    }

    @Override
    public List<PbPartyEvaluationResult> selectPbPartyEvaluationResultList(PbPartyEvaluationResult info) {
        return mapper.selectPbPartyEvaluationResultList(info);
    }

    @Override
    public int insertPbPartyEvaluationResult(PbPartyEvaluationResult info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertPbPartyEvaluationResult(info);
    }

    @Override
    public int updatePbPartyEvaluationResult(PbPartyEvaluationResult info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updatePbPartyEvaluationResult(info);
    }

    @Override
    public int deletePbPartyEvaluationResultByIds(Long[] ids) {
        return mapper.deletePbPartyEvaluationResultByIds(ids);
    }

    @Override
    public int deletePbPartyEvaluationResultById(Long id) {
        return mapper.deletePbPartyEvaluationResultById(id);
    }

    @Override
    public Map<String, List<CommonBaseCount>> starVillage(String evaluationBatchYear) {
        // 1. 获取当前年度的评价批次
        List<SysDictData> townEvaluationBatch = DictUtils.getDictCache(evaluationBatchYear);
        String evaluationBatch = DateUtil.thisYear() + "";
        if (townEvaluationBatch != null && !townEvaluationBatch.isEmpty()) {
            // 获取sort最大的批次
            townEvaluationBatch.sort(Comparator.comparingLong(SysDictData::getDictSort));
            evaluationBatch = townEvaluationBatch.get(townEvaluationBatch.size() - 1).getDictValue();
        }

        // 2. 查询数据
        List<CommonBaseCount> dbCounts = mapper.groupByStarLevelAndTownType(evaluationBatch);
        
        // 3. 初始化结果集
        Map<String, List<CommonBaseCount>> result = new LinkedHashMap<>();
        
        // 4. 按城乡类型分组
        Map<String, List<CommonBaseCount>> townTypeGroups = new HashMap<>();
        
        // 4.1 添加默认的城乡类型
        List<String> defaultTownTypes = Arrays.asList("城市", "农村");
        for (String townType : defaultTownTypes) {
            townTypeGroups.put(townType, new ArrayList<>());
        }
        
        // 4.2 如果有数据库数据，则更新对应的类型
        if (dbCounts != null && !dbCounts.isEmpty()) {
            dbCounts.forEach(item -> {
                String townType = item.getLable();
                if (townType != null) {
                    townTypeGroups.computeIfAbsent(townType, k -> new ArrayList<>()).add(item);
                }
            });
        }
        
        // 5. 确保每个城乡类型的每个星级都有数据（没有的补0）
        for (Map.Entry<String, List<CommonBaseCount>> entry : townTypeGroups.entrySet()) {
            String townType = entry.getKey();
            List<CommonBaseCount> counts = entry.getValue();
            
            // 创建1-5星的列表
            List<CommonBaseCount> starCounts = new ArrayList<>();
            for (int i = 1; i <= 5; i++) {
                final int starLevel = i;
                CommonBaseCount count = counts.stream()
                    .filter(Objects::nonNull)
                    .filter(c -> c.getStarLevel() != null && c.getStarLevel() == starLevel)
                    .findFirst()
                    .orElseGet(() -> {
                        CommonBaseCount zeroCount = new CommonBaseCount();
                        zeroCount.setLable(townType);
                        zeroCount.setType(starLevel);
                        zeroCount.setStarLevel(starLevel);
                        zeroCount.setCount(0);
                        return zeroCount;
                    });
                starCounts.add(count);
            }
            
            // 按星级排序
            starCounts.sort(Comparator.comparingInt(CommonBaseCount::getType));
            result.put(townType, starCounts);
        }
        return result;
    }

}
