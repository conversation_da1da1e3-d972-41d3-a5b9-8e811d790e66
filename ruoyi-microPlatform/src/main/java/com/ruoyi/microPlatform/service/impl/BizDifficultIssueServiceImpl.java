package com.ruoyi.microPlatform.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.BizDifficultIssue;
import com.ruoyi.microPlatform.domain.dto.DifficultIssueStatsDTO;
import com.ruoyi.microPlatform.mapper.BizDifficultIssueMapper;
import com.ruoyi.microPlatform.service.IBizDifficultIssueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 疑难问题汇聚事项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
public class BizDifficultIssueServiceImpl implements IBizDifficultIssueService {
    @Autowired
    private BizDifficultIssueMapper bizDifficultIssueMapper;

    /**
     * 查询疑难问题汇聚事项
     *
     * @param id 疑难问题汇聚事项主键
     * @return 疑难问题汇聚事项
     */
    @Override
    public BizDifficultIssue selectBizDifficultIssueById(Long id) {
        return bizDifficultIssueMapper.selectBizDifficultIssueById(id);
    }

    /**
     * 查询疑难问题汇聚事项列表
     *
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 疑难问题汇聚事项
     */
    @Override
    public List<BizDifficultIssue> selectBizDifficultIssueList(BizDifficultIssue bizDifficultIssue) {
        return bizDifficultIssueMapper.selectBizDifficultIssueList(bizDifficultIssue);
    }

    /**
     * 新增疑难问题汇聚事项
     *
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 结果
     */
    @Override
    public int insertBizDifficultIssue(BizDifficultIssue bizDifficultIssue) {
        return bizDifficultIssueMapper.insertBizDifficultIssue(bizDifficultIssue);
    }

    /**
     * 修改疑难问题汇聚事项
     *
     * @param bizDifficultIssue 疑难问题汇聚事项
     * @return 结果
     */
    @Override
    public int updateBizDifficultIssue(BizDifficultIssue bizDifficultIssue) {
        return bizDifficultIssueMapper.updateBizDifficultIssue(bizDifficultIssue);
    }

    /**
     * 批量删除疑难问题汇聚事项
     *
     * @param ids 需要删除的疑难问题汇聚事项主键
     * @return 结果
     */
    @Override
    public int deleteBizDifficultIssueByIds(Long[] ids) {
        return bizDifficultIssueMapper.deleteBizDifficultIssueByIds(ids);
    }
    
    /**
     * 根据状态统计数量
     *
     * @param status 状态
     * @return 数量
     */
    @Override
    public int countByStatus(Integer status) {
        return bizDifficultIssueMapper.countByStatus(status);
    }


    /**
     * 删除疑难问题汇聚事项信息
     *
     * @param id 疑难问题汇聚事项主键
     * @return 结果
     */
    @Override
    public int deleteBizDifficultIssueById(Long id) {
        return bizDifficultIssueMapper.deleteBizDifficultIssueById(id);
    }
}
