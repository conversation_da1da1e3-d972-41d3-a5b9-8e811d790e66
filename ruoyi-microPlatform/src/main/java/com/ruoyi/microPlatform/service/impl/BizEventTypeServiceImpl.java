package com.ruoyi.microPlatform.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.BizEventTypeMapper;
import com.ruoyi.microPlatform.domain.BizEventType;
import com.ruoyi.microPlatform.service.IBizEventTypeService;

/**
 * 问题类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Service
public class BizEventTypeServiceImpl implements IBizEventTypeService 
{
    @Autowired
    private BizEventTypeMapper bizEventTypeMapper;

    /**
     * 查询问题类型
     * 
     * @param id 问题类型主键
     * @return 问题类型
     */
    @Override
    public BizEventType selectBizEventTypeById(Long id)
    {
        return bizEventTypeMapper.selectBizEventTypeById(id);
    }

    /**
     * 查询问题类型列表
     * 
     * @param bizEventType 问题类型
     * @return 问题类型
     */
    @Override
    public List<BizEventType> selectBizEventTypeList(BizEventType bizEventType)
    {
        return bizEventTypeMapper.selectBizEventTypeList(bizEventType);
    }

    /**
     * 新增问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    @Override
    public int insertBizEventType(BizEventType bizEventType)
    {
        return bizEventTypeMapper.insertBizEventType(bizEventType);
    }

    /**
     * 修改问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    @Override
    public int updateBizEventType(BizEventType bizEventType)
    {
        return bizEventTypeMapper.updateBizEventType(bizEventType);
    }

    /**
     * 批量删除问题类型
     * 
     * @param ids 需要删除的问题类型主键集合
     * @return 结果
     */
    @Override
    public int deleteBizEventTypeByIds(Long[] ids)
    {
        return bizEventTypeMapper.deleteBizEventTypeByIds(ids);
    }

    /**
     * 删除问题类型信息
     * 
     * @param id 问题类型主键
     * @return 结果
     */
    @Override
    public int deleteBizEventTypeById(Long id)
    {
        return bizEventTypeMapper.deleteBizEventTypeById(id);
    }
}
