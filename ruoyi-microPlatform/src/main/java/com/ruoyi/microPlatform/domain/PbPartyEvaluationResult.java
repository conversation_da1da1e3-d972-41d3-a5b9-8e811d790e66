package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 党建评价结果表 pb_party_evaluation_result
 */
@Data
public class PbPartyEvaluationResult extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 评价结果编号 */
    private String resultCode;

    /** 评价对象ID（党员ID或党组织ID） */
    private Long targetId;

    /** 评价对象名称 */
    private String targetName;

    /** 评价对象类型（MEMBER党员/BRANCH党支部/COMMUNITY星级社区） */
    private String targetType;

    /** 所属部门ID */
    private Long deptId;

    /** 所属县（市、区） */
    private String county;

    /** 所属街道/乡镇 */
    private String country;

    /** 所属村/社区 */
    private String town;

    /** 城市/农村 */
    private String townType;

    /** 评价周期（月度/季度/年度） */
    private String evaluationPeriod;

    /** 评价批次 */
    private String evaluationBatch;

    /** 评价年份 */
    private Integer evaluationYear;

    /** 评价月份 */
    private Integer evaluationMonth;

    /** 总得分 */
    private BigDecimal totalScore;

    /** 评价等级（优秀/良好/一般/较差） */
    private String evaluationLevel;

    /** 星级评定（1-5星） */
    private Integer starLevel;

    /** 各维度得分详情（JSON格式存储） */
    private String dimensionScores;

    /** 评价明细说明 */
    private String evaluationDetail;

    /** 改进建议 */
    private String improvementSuggestions;

    /** 评价人ID */
    private Long evaluatorId;

    /** 评价人姓名 */
    private String evaluatorName;

    /** 审核状态（0待审核 1已审核 2已公示） */
    private Integer auditStatus;

    /** 审核时间 */
    private Date auditTime;
}
