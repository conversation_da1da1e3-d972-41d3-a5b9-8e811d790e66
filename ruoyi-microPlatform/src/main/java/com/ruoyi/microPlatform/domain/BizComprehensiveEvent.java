package com.ruoyi.microPlatform.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 综合事项对象 biz_comprehensive_event
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public class BizComprehensiveEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 事件唯一编号 */
    @Excel(name = "事件唯一编号")
    private String eventId;

    /** 事件归属部门 */
    @Excel(name = "事件归属部门")
    private Long deptId;

    /** 事件归属县区 */
    @Excel(name = "事件归属县区")
    private String county;

    /** 事件归属乡镇/街道 */
    @Excel(name = "事件归属乡镇/街道")
    private String country;

    /** 事件归属村/社区 */
    @Excel(name = "事件归属村/社区")
    private String town;

    /** 事件归属网格ID */
    @Excel(name = "事件归属网格ID")
    private Long gridId;

    /** 事件归属网格名称 */
    @Excel(name = "事件归属网格名称")
    private String gridName;

    /** 源数据ID */
    @Excel(name = "源数据ID")
    private Long sourceId;

    /** 源数据表名 */
    @Excel(name = "源数据表名")
    private String sourceTable;

    /** 事件类型（1=一般事项,2=突发应急事项,3=疑难事项） */
    @Excel(name = "事件类型", readConverterExp = "1==一般事项,2=突发应急事项,3=疑难事项")
    private Integer eventType;

    /** 综合事项来源 */
    @Excel(name = "综合事项来源")
    private String eventSource;

    /** 事件发生地址描述 */
    @Excel(name = "事件发生地址描述")
    private String eventLocation;

    /** 事件标题 */
    @Excel(name = "事件标题")
    private String eventTitle;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String eventDescription;

    /** 问题(事项)类型 */
    @Excel(name = "问题(事项)类型")
    private String eventMajor;

    /** 问题(事项)类型编码 */
    @Excel(name = "问题(事项)类型编码")
    private String eventMinor;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 办理开始时间 */
    @Excel(name = "办理开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTime;

    /** 办理截止时间 */
    @Excel(name = "办理截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventDeadline;

    /** 上报人id */
    @Excel(name = "上报人id")
    private Long reportUserId;

    /** 上报人姓名 */
    @Excel(name = "上报人姓名")
    private String reportName;

    /** 上报时间 */
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /** 上报人手机号 */
    @Excel(name = "上报人手机号")
    private String reportPhone;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 事项状态(0=办理中,1=办结申请中,2=已办结) */
    @Excel(name = "事项状态", readConverterExp = "0=办理中,1=办结申请中,2=已办结")
    private Integer eventStatus;

    /** 实际办结时间 */
    @Excel(name = "实际办结时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date finishEndTime;

    /** 工单中的状态 */
    @Excel(name = "工单中的状态")
    private String workStatus;

    /** 当前处置层级（4=市,5=县区,6=街道/乡镇,7=村镇/社区,8=网格） */
    @Excel(name = "当前处置层级", readConverterExp = "4=市,5=县区,6=街道/乡镇,7=村镇/社区,8=网格")
    private Integer currentHandleLevel;

    /** 关联工单ID */
    @Excel(name = "关联工单ID")
    private Long relatedWorkOrderId;

    /** 处置内容 */
    @Excel(name = "处置内容")
    private String disposalContent;

    /** 办结结果 */
    @Excel(name = "办结结果")
    private String disposalResult;

    /** 附件（图片/视频/文件，逗号分隔） */
    @Excel(name = "附件")
    private String accessory;

    /** 事件主体类型（居民、企业、部门） */
    @Excel(name = "事件主体类型", readConverterExp = "居=民、企业、部门")
    private String userType;

    /** 事件当事人 */
    @Excel(name = "事件当事人")
    private String userName;

    /** 事件当事人唯一标识 */
    @Excel(name = "事件当事人唯一标识")
    private String userUid;

    /** 唯一标识类型（手机号、证件号） */
    @Excel(name = "唯一标识类型", readConverterExp = "手=机号、证件号")
    private String uidType;

    /** 是否回访(0=否,1=是) */
    @Excel(name = "是否回访(0=否,1=是) ")
    private Integer isReturnVisit;

    /** 回访满意度 */
    @Excel(name = "回访满意度")
    private String returnVisitScore;

    /** 回访内容 */
    @Excel(name = "回访内容")
    private String returnVisitContent;

    /** 回访时间 */
    @Excel(name = "回访时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnVisitTime;

    /** 是否评价(0=否,1=是) */
    @Excel(name = "是否评价(0=否,1=是)")
    private Integer isSatisfaction;

    /** 满意度(1=满意,2=基本满意,3=不满意) */
    @Excel(name = "满意度(1=满意,2=基本满意,3=不满意)")
    private Integer satisfaction;

    /** 评价时间 */
    @Excel(name = "评价时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date satisfactionTime;

    /** 是否重办(0=否,1=是) */
    @Excel(name = "是否重办(0=否,1=是)")
    private Integer isReapply;

    /** 重办时间 */
    @Excel(name = "重办时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reapplyTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setEventId(String eventId) 
    {
        this.eventId = eventId;
    }

    public String getEventId() 
    {
        return eventId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setSourceId(Long sourceId) 
    {
        this.sourceId = sourceId;
    }

    public Long getSourceId() 
    {
        return sourceId;
    }
    public void setSourceTable(String sourceTable) 
    {
        this.sourceTable = sourceTable;
    }

    public String getSourceTable() 
    {
        return sourceTable;
    }
    public void setEventType(Integer eventType) 
    {
        this.eventType = eventType;
    }

    public Integer getEventType() 
    {
        return eventType;
    }
    public void setEventSource(String eventSource) 
    {
        this.eventSource = eventSource;
    }

    public String getEventSource() 
    {
        return eventSource;
    }
    public void setEventLocation(String eventLocation) 
    {
        this.eventLocation = eventLocation;
    }

    public String getEventLocation() 
    {
        return eventLocation;
    }
    public void setEventTitle(String eventTitle) 
    {
        this.eventTitle = eventTitle;
    }

    public String getEventTitle() 
    {
        return eventTitle;
    }
    public void setEventDescription(String eventDescription) 
    {
        this.eventDescription = eventDescription;
    }

    public String getEventDescription() 
    {
        return eventDescription;
    }
    public void setEventMajor(String eventMajor) 
    {
        this.eventMajor = eventMajor;
    }

    public String getEventMajor() 
    {
        return eventMajor;
    }
    public void setEventMinor(String eventMinor) 
    {
        this.eventMinor = eventMinor;
    }

    public String getEventMinor() 
    {
        return eventMinor;
    }
    public void setUrgencyLevel(String urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public String getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setEventStartTime(Date eventStartTime) 
    {
        this.eventStartTime = eventStartTime;
    }

    public Date getEventStartTime() 
    {
        return eventStartTime;
    }
    public void setEventDeadline(Date eventDeadline) 
    {
        this.eventDeadline = eventDeadline;
    }

    public Date getEventDeadline() 
    {
        return eventDeadline;
    }
    public void setReportUserId(Long reportUserId) 
    {
        this.reportUserId = reportUserId;
    }

    public Long getReportUserId() 
    {
        return reportUserId;
    }
    public void setReportName(String reportName) 
    {
        this.reportName = reportName;
    }

    public String getReportName() 
    {
        return reportName;
    }
    public void setReportTime(Date reportTime) 
    {
        this.reportTime = reportTime;
    }

    public Date getReportTime() 
    {
        return reportTime;
    }
    public void setReportPhone(String reportPhone) 
    {
        this.reportPhone = reportPhone;
    }

    public String getReportPhone() 
    {
        return reportPhone;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setEventStatus(Integer eventStatus) 
    {
        this.eventStatus = eventStatus;
    }

    public Integer getEventStatus() 
    {
        return eventStatus;
    }
    public void setFinishEndTime(Date finishEndTime) 
    {
        this.finishEndTime = finishEndTime;
    }

    public Date getFinishEndTime() 
    {
        return finishEndTime;
    }
    public void setWorkStatus(String workStatus) 
    {
        this.workStatus = workStatus;
    }

    public String getWorkStatus() 
    {
        return workStatus;
    }
    public void setCurrentHandleLevel(Integer currentHandleLevel) 
    {
        this.currentHandleLevel = currentHandleLevel;
    }

    public Integer getCurrentHandleLevel() 
    {
        return currentHandleLevel;
    }
    public void setRelatedWorkOrderId(Long relatedWorkOrderId) 
    {
        this.relatedWorkOrderId = relatedWorkOrderId;
    }

    public Long getRelatedWorkOrderId() 
    {
        return relatedWorkOrderId;
    }
    public void setDisposalContent(String disposalContent) 
    {
        this.disposalContent = disposalContent;
    }

    public String getDisposalContent() 
    {
        return disposalContent;
    }
    public void setDisposalResult(String disposalResult) 
    {
        this.disposalResult = disposalResult;
    }

    public String getDisposalResult() 
    {
        return disposalResult;
    }
    public void setAccessory(String accessory) 
    {
        this.accessory = accessory;
    }

    public String getAccessory() 
    {
        return accessory;
    }
    public void setUserType(String userType) 
    {
        this.userType = userType;
    }

    public String getUserType() 
    {
        return userType;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setUserUid(String userUid) 
    {
        this.userUid = userUid;
    }

    public String getUserUid() 
    {
        return userUid;
    }
    public void setUidType(String uidType) 
    {
        this.uidType = uidType;
    }

    public String getUidType() 
    {
        return uidType;
    }
    public void setIsReturnVisit(Integer isReturnVisit) 
    {
        this.isReturnVisit = isReturnVisit;
    }

    public Integer getIsReturnVisit() 
    {
        return isReturnVisit;
    }
    public void setReturnVisitScore(String returnVisitScore) 
    {
        this.returnVisitScore = returnVisitScore;
    }

    public String getReturnVisitScore() 
    {
        return returnVisitScore;
    }
    public void setReturnVisitContent(String returnVisitContent) 
    {
        this.returnVisitContent = returnVisitContent;
    }

    public String getReturnVisitContent() 
    {
        return returnVisitContent;
    }
    public void setReturnVisitTime(Date returnVisitTime) 
    {
        this.returnVisitTime = returnVisitTime;
    }

    public Date getReturnVisitTime() 
    {
        return returnVisitTime;
    }
    public void setIsSatisfaction(Integer isSatisfaction) 
    {
        this.isSatisfaction = isSatisfaction;
    }

    public Integer getIsSatisfaction() 
    {
        return isSatisfaction;
    }
    public void setSatisfaction(Integer satisfaction) 
    {
        this.satisfaction = satisfaction;
    }

    public Integer getSatisfaction() 
    {
        return satisfaction;
    }
    public void setSatisfactionTime(Date satisfactionTime) 
    {
        this.satisfactionTime = satisfactionTime;
    }

    public Date getSatisfactionTime() 
    {
        return satisfactionTime;
    }
    public void setIsReapply(Integer isReapply) 
    {
        this.isReapply = isReapply;
    }

    public Integer getIsReapply() 
    {
        return isReapply;
    }
    public void setReapplyTime(Date reapplyTime) 
    {
        this.reapplyTime = reapplyTime;
    }

    public Date getReapplyTime() 
    {
        return reapplyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .append("sourceId", getSourceId())
            .append("sourceTable", getSourceTable())
            .append("eventType", getEventType())
            .append("eventSource", getEventSource())
            .append("eventLocation", getEventLocation())
            .append("eventTitle", getEventTitle())
            .append("eventDescription", getEventDescription())
            .append("eventMajor", getEventMajor())
            .append("eventMinor", getEventMinor())
            .append("urgencyLevel", getUrgencyLevel())
            .append("eventStartTime", getEventStartTime())
            .append("eventDeadline", getEventDeadline())
            .append("reportUserId", getReportUserId())
            .append("reportName", getReportName())
            .append("reportTime", getReportTime())
            .append("reportPhone", getReportPhone())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("eventStatus", getEventStatus())
            .append("finishEndTime", getFinishEndTime())
            .append("workStatus", getWorkStatus())
            .append("currentHandleLevel", getCurrentHandleLevel())
            .append("relatedWorkOrderId", getRelatedWorkOrderId())
            .append("disposalContent", getDisposalContent())
            .append("disposalResult", getDisposalResult())
            .append("accessory", getAccessory())
            .append("userType", getUserType())
            .append("userName", getUserName())
            .append("userUid", getUserUid())
            .append("uidType", getUidType())
            .append("isReturnVisit", getIsReturnVisit())
            .append("returnVisitScore", getReturnVisitScore())
            .append("returnVisitContent", getReturnVisitContent())
            .append("returnVisitTime", getReturnVisitTime())
            .append("isSatisfaction", getIsSatisfaction())
            .append("satisfaction", getSatisfaction())
            .append("satisfactionTime", getSatisfactionTime())
            .append("isReapply", getIsReapply())
            .append("reapplyTime", getReapplyTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
