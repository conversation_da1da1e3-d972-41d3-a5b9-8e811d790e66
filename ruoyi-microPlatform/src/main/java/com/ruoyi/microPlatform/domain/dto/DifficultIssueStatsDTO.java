package com.ruoyi.microPlatform.domain.dto;

import java.io.Serializable;
import java.util.Map;

/**
 * 疑难问题统计DTO
 */
public class DifficultIssueStatsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /** 总数量 */
    private Integer totalCount;
    
    /** 按来源系统分组的数量 */
    private Map<String, Integer> statsBySourceSystem;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Map<String, Integer> getStatsBySourceSystem() {
        return statsBySourceSystem;
    }

    public void setStatsBySourceSystem(Map<String, Integer> statsBySourceSystem) {
        this.statsBySourceSystem = statsBySourceSystem;
    }
}
