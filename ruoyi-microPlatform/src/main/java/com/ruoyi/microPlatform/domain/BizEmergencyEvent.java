package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 应急突发事件对象 biz_emergency_event
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizEmergencyEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 物理主键，自增ID */
    private Long id;

    /** 事件唯一编号 */
    @Excel(name = "事件编号")
    private String eventId;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 事件归属县区 */
    @Excel(name = "事件归属县区")
    private String county;

    /** 事件归属乡镇/街道 */
    @Excel(name = "事件归属乡镇/街道")
    private String country;

    /** 事件归属村/社区 */
    @Excel(name = "事件归属村/社区")
    private String town;

    /** 事件归属网格ID */
    @Excel(name = "事件归属网格ID")
    private Long gridId;

    /** 事件归属网格名称 */
    @Excel(name = "事件归属网格名称")
    private String gridName;

    /** 事件标题 */
    @Excel(name = "事件标题")
    private String eventTitle;

    /** 事件类别 */
    @Excel(name = "事件类别")
    private String eventCategory;

    /** 事件等级 */
    @Excel(name = "事件等级")
    private String eventLevel;

    /** 问题(事项)类型 */
    @Excel(name = "问题(事项)类型")
    private String eventMajor;

    /** 问题(事项)类型编码 */
    @Excel(name = "问题(事项)类型编码")
    private String eventMinor;

    /** 事发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    /** 事发地点 */
    @Excel(name = "事发地点")
    private String eventLocation;

    /** 经度 */
    @Excel(name = "经度")
    private String lng;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 事件详细描述 */
    @Excel(name = "事件详细描述")
    private String eventDescription;

    /** 上报人ID */
    @Excel(name = "上报人ID")
    private Long reporterUserId;

    /** 上报人姓名 */
    @Excel(name = "上报人姓名")
    private String reporterName;

    /** 上报人电话 */
    @Excel(name = "上报人电话")
    private String reporterPhone;

    /** 上报单位ID */
    @Excel(name = "上报单位ID")
    private Long reporterDeptId;

    /** 上报单位名称 */
    @Excel(name = "上报单位名称")
    private String reporterDeptName;

    /** 是否自动绕流 */
    @Excel(name = "是否自动绕流")
    private Integer isAutoBypass;

    /** 签收状态，0=未签收，1=已签收 */
    @Excel(name = "签收状态")
    private Integer acceptStatus;

    /** 签收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;

    /** 事件来源 */
    @Excel(name = "事件来源")
    private Integer eventSource;

    /** 事件上报层级 */
    @Excel(name = "事件上报层级")
    private Integer eventReportLevel;

    /** 初次处置单位层级 */
    @Excel(name = "初次处置单位层级")
    private Integer currentDeptLevel;

    /** 初次处置单位ID */
    @Excel(name = "初次处置单位ID")
    private String currentDeptId;

    /** 事件状态，0=处理中,1=已办结 */
    @Excel(name = "事件状态")
    private Integer eventStatus;

    /** 流转状态 */
    @Excel(name = "流转状态")
    private Integer flowStatus;

    /** 关联工单ID */
    @Excel(name = "关联工单ID")
    private Long relatedWorkOrderId;

    /** 附件 */
    @Excel(name = "附件")
    private String accessory;
}
