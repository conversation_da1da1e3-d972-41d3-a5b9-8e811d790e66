package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 疑难问题汇聚事项对象 biz_difficult_issue
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
public class BizDifficultIssue extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 事项唯一编号
     */
    @Excel(name = "事项唯一编号")
    private String eventId;

    /**
     * 来源系统
     */
    @Excel(name = "来源系统")
    private String sourceSystem;

    /**
     * 原系统工单号
     */
    @Excel(name = "原系统工单号")
    private String originalId;

    /**
     * 事件标题
     */
    @Excel(name = "事件标题")
    private String eventTitle;

    /**
     * 事件描述
     */
    @Excel(name = "事件描述")
    private String eventDescription;

    /**
     * 疑难原因说明
     */
    @Excel(name = "疑难原因说明")
    private String difficultReason;

    /**
     * 原平台处理过程详述
     */
    @Excel(name = "原平台处理过程详述")
    private String originalProcess;

    /**
     * 问题(事项)类型
     */
    @Excel(name = "问题(事项)类型")
    private String eventMajor;

    /**
     * 问题(事项)类型编码
     */
    @Excel(name = "问题(事项)类型编码")
    private String eventMinor;

    /**
     * 事件发生地
     */
    @Excel(name = "事件发生地")
    private String eventLocation;

    /**
     * 事发时间
     */
    @Excel(name = "事发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String eventTime;

    /**
     * 事发涉及县
     */
    @Excel(name = "事发涉及县")
    private String county;

    /**
     * 事发涉及乡镇/街道
     */
    @Excel(name = "事发涉及乡镇/街道")
    private String country;

    /**
     * 事发涉及村/社区
     */
    @Excel(name = "事发涉及村/社区")
    private String town;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String lat;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String lng;

    /**
     * 是否接收(0=未处理,1=接收处理,2=退回)
     */
    @Excel(name = "是否接收(0=未处理,1=接收处理,2=退回)")
    private Integer status;

    /**
     * 流转状态(0=未处理,1=建立台账)
     */
    @Excel(name = "流转状态(0=未处理,1=建立台账)")
    private Integer flowStatus;

    /**
     * 研判结果
     */
    @Excel(name = "研判结果")
    private String judgeResult;

    /**
     * 研判时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "研判时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date judgeTime;

    /**
     * 关联的工单ID
     */
    @Excel(name = "关联的工单ID")
    private Long workOrderId;

    /**
     * 附件（图片/视频/文件，逗号分隔）
     */
    @Excel(name = "附件", readConverterExp = "图片/视频/文件，逗号分隔")
    private String accessory;

    /**
     * 事件主体类型（居民、企业、部门）
     */
    @Excel(name = "事件主体类型", readConverterExp = "居=民、企业、部门")
    private String userType;

    /**
     * 事件当事人
     */
    @Excel(name = "事件当事人")
    private String userName;

    /**
     * 事件当事人唯一标识
     */
    @Excel(name = "事件当事人唯一标识")
    private String userUid;

    /**
     * 唯一标识类型（手机号、证件号）
     */
    @Excel(name = "唯一标识类型", readConverterExp = "手=机号、证件号")
    private String uidType;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventId() {
        return eventId;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setOriginalId(String originalId) {
        this.originalId = originalId;
    }

    public String getOriginalId() {
        return originalId;
    }

    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }

    public String getEventTitle() {
        return eventTitle;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setDifficultReason(String difficultReason) {
        this.difficultReason = difficultReason;
    }

    public String getDifficultReason() {
        return difficultReason;
    }

    public void setOriginalProcess(String originalProcess) {
        this.originalProcess = originalProcess;
    }

    public String getOriginalProcess() {
        return originalProcess;
    }

    public void setEventMajor(String eventMajor) {
        this.eventMajor = eventMajor;
    }

    public String getEventMajor() {
        return eventMajor;
    }

    public void setEventMinor(String eventMinor) {
        this.eventMinor = eventMinor;
    }

    public String getEventMinor() {
        return eventMinor;
    }

    public void setEventLocation(String eventLocation) {
        this.eventLocation = eventLocation;
    }

    public String getEventLocation() {
        return eventLocation;
    }

    public void setEventTime(String eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventTime() {
        return eventTime;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCounty() {
        return county;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTown() {
        return town;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLat() {
        return lat;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLng() {
        return lng;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setJudgeResult(String judgeResult) {
        this.judgeResult = judgeResult;
    }

    public String getJudgeResult() {
        return judgeResult;
    }

    public void setJudgeTime(Date judgeTime) {
        this.judgeTime = judgeTime;
    }

    public Date getJudgeTime() {
        return judgeTime;
    }

    public void setWorkOrderId(Long workOrderId) {
        this.workOrderId = workOrderId;
    }

    public Long getWorkOrderId() {
        return workOrderId;
    }

    public void setAccessory(String accessory) {
        this.accessory = accessory;
    }

    public String getAccessory() {
        return accessory;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserUid(String userUid) {
        this.userUid = userUid;
    }

    public String getUserUid() {
        return userUid;
    }

    public void setUidType(String uidType) {
        this.uidType = uidType;
    }

    public String getUidType() {
        return uidType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("eventId", getEventId())
                .append("sourceSystem", getSourceSystem())
                .append("originalId", getOriginalId())
                .append("eventTitle", getEventTitle())
                .append("eventDescription", getEventDescription())
                .append("difficultReason", getDifficultReason())
                .append("originalProcess", getOriginalProcess())
                .append("eventMajor", getEventMajor())
                .append("eventMinor", getEventMinor())
                .append("eventLocation", getEventLocation())
                .append("eventTime", getEventTime())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("lat", getLat())
                .append("lng", getLng())
                .append("status", getStatus())
                .append("flowStatus", getFlowStatus())
                .append("judgeResult", getJudgeResult())
                .append("judgeTime", getJudgeTime())
                .append("workOrderId", getWorkOrderId())
                .append("accessory", getAccessory())
                .append("userType", getUserType())
                .append("userName", getUserName())
                .append("userUid", getUserUid())
                .append("uidType", getUidType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
