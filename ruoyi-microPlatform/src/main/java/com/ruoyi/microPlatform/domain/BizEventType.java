package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 问题类型对象 biz_event_type
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizEventType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 父级ID，大类为0，小类/子类指向父类ID */
    @Excel(name = "父级ID")
    private Long parentId;

    /** 类型名称 */
    @Excel(name = "类型名称")
    private String typeName;

    /** 类型编码（唯一） */
    @Excel(name = "类型编码")
    private String typeCode;

    /** 默认处置时限（单位：天） */
    @Excel(name = "默认处置时限", readConverterExp = "天=")
    private Integer defaultTimeLimit;

    /** 优先级规则 */
    @Excel(name = "优先级规则")
    private String priorityRule;

    /** 默认主责部门/指引部门 */
    @Excel(name = "默认主责部门/指引部门")
    private String defaultDept;

    /** 标准处理流程模板 */
    @Excel(name = "标准处理流程模板")
    private String processTemplate;

    /** 是否启用：1=启用，0=禁用 */
    @Excel(name = "是否启用")
    private Integer enabled;

    /** 层级：1=大类，2=小类，3/4=预留扩展 */
    @Excel(name = "层级")
    private Integer level;

    /** 排序号，越小优先 */
    @Excel(name = "排序号")
    private Integer sort;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
