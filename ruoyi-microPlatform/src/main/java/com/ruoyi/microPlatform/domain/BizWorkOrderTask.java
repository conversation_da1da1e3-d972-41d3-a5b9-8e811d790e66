package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 主协办任务对象 biz_work_order_task
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizWorkOrderTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 关联的工单ID */
    @Excel(name = "工单ID")
    private Long workOrderId;

    /** 派发任务的部门ID */
    @Excel(name = "派发部门ID")
    private Long assignDeptId;

    /** 派发任务的部门名称 */
    @Excel(name = "派发部门名称")
    private String assignDeptName;

    /** 派发任务的部门级别 */
    @Excel(name = "派发部门级别")
    private Integer assignDeptLevel;

    /** 处理方向 */
    @Excel(name = "处理方向")
    private Integer flowDirection;

    /** 是否市直/县直单位 */
    @Excel(name = "是否市直/县直单位")
    private Integer isDepartment;

    /** 承办部门单位 */
    @Excel(name = "承办部门ID")
    private Long handleDeptId;

    /** 承办单位名称 */
    @Excel(name = "承办单位名称")
    private String handleDeptName;

    /** 单位层级 */
    @Excel(name = "单位层级")
    private Integer handleDeptLevel;

    /** 主办/协办类型 */
    @Excel(name = "主办/协办类型")
    private Integer handleType;

    /** 主办id */
    @Excel(name = "主办id")
    private Long hostTaskId;

    /** 任务说明 */
    @Excel(name = "任务说明")
    private String taskContent;

    /** 任务状态 */
    @Excel(name = "任务状态")
    private Integer taskStatus;

    /** 是否启动街乡吹哨机制 */
    @Excel(name = "是否启动街乡吹哨机制")
    private Integer isWhistleblowing;

    /** 吹哨时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "吹哨时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date whistleblowingTime;

    /** 签收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /** 签收人ID */
    @Excel(name = "签收人ID")
    private Long signUserId;

    /** 签收人姓名 */
    @Excel(name = "签收人姓名")
    private String signUserName;

    /** 退回原因 */
    @Excel(name = "退回原因")
    private String returnReason;

    /** 退回时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

    /** 转交目标部门ID */
    @Excel(name = "转交目标部门ID")
    private Long transferDeptId;

    /** 转交目标部门名称 */
    @Excel(name = "转交目标部门名称")
    private String transferDeptName;

    /** 转交目标部门级别 */
    @Excel(name = "转交目标部门级别")
    private Integer transferDeptLevel;

    /** 转交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "转交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transferTime;

    /** 任务办结时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务办结时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /** 序号 */
    @Excel(name = "序号")
    private Integer sortNumber;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer isEnable;
}
