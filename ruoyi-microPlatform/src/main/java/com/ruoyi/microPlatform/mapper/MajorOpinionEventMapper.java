package com.ruoyi.microPlatform.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.MajorOpinionEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 重大舆情信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
@Mapper
public interface MajorOpinionEventMapper 
{
    /**
     * 查询重大舆情信息
     * 
     * @param id 重大舆情信息主键
     * @return 重大舆情信息
     */
    public MajorOpinionEvent selectMajorOpinionEventById(Long id);

    /**
     * 查询重大舆情信息列表
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 重大舆情信息集合
     */
    public List<MajorOpinionEvent> selectMajorOpinionEventList(MajorOpinionEvent majorOpinionEvent);

    /**
     * 新增重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    public int insertMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent);

    /**
     * 修改重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    public int updateMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent);

    /**
     * 删除重大舆情信息
     * 
     * @param id 重大舆情信息主键
     * @return 结果
     */
    public int deleteMajorOpinionEventById(Long id);

    /**
     * 批量删除重大舆情信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMajorOpinionEventByIds(Long[] ids);
    
    /**
     * 按日期范围统计舆情事件数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 包含日期和数量的Map列表
     */
    public List<Map<String, Object>> countByDateRange(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 根据部门ID统计舆情数量
     * @param deptId 部门ID
     * @return 舆情数量
     */
    public int countByDeptId(@Param("deptId") Long deptId);
    
    /**
     * 获取每个区域的舆情数量
     * @return 区域舆情数量列表，包含区域名和对应的数量
     */
    public List<Map<String, Object>> selectOpinionCountByArea();

    /**
     * 获取最新的舆情事项
     * @param limit 限制数量
     * @return 舆情事项列表
     */
    List<MajorOpinionEvent> selectLatestOpinionEvents(int limit);
}
