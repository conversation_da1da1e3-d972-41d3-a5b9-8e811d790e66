package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.BizEventType;

/**
 * 问题类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
public interface BizEventTypeMapper 
{
    /**
     * 查询问题类型
     * 
     * @param id 问题类型主键
     * @return 问题类型
     */
    public BizEventType selectBizEventTypeById(Long id);

    /**
     * 查询问题类型列表
     * 
     * @param bizEventType 问题类型
     * @return 问题类型集合
     */
    public List<BizEventType> selectBizEventTypeList(BizEventType bizEventType);

    /**
     * 新增问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    public int insertBizEventType(BizEventType bizEventType);

    /**
     * 修改问题类型
     * 
     * @param bizEventType 问题类型
     * @return 结果
     */
    public int updateBizEventType(BizEventType bizEventType);

    /**
     * 删除问题类型
     * 
     * @param id 问题类型主键
     * @return 结果
     */
    public int deleteBizEventTypeById(Long id);

    /**
     * 批量删除问题类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizEventTypeByIds(Long[] ids);
}
