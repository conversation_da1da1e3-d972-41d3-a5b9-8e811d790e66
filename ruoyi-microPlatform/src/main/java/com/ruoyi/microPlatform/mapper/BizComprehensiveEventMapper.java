package com.ruoyi.microPlatform.mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.BizComprehensiveEvent;
import org.apache.ibatis.annotations.Param;

/**
 * 综合事项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface BizComprehensiveEventMapper {
    /**
     * 查询综合事项
     *
     * @param id 综合事项主键
     * @return 综合事项
     */
    public BizComprehensiveEvent selectBizComprehensiveEventById(Long id);

    /**
     * 查询综合事项列表
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 综合事项集合
     */
    public List<BizComprehensiveEvent> selectBizComprehensiveEventList(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 获取热门事项TOP6
     * @param param 查询参数，包含时间维度
     * @return 热门事项列表，按数量降序排序，最多返回6条
     */
    List<Map<String, Object>> selectTop6EventMajors(BigdataParam param);

    /**
     * 新增综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    public int insertBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 修改综合事项
     *
     * @param bizComprehensiveEvent 综合事项
     * @return 结果
     */
    public int updateBizComprehensiveEvent(BizComprehensiveEvent bizComprehensiveEvent);

    /**
     * 删除综合事项
     *
     * @param id 综合事项主键
     * @return 结果
     */
    public int deleteBizComprehensiveEventById(Long id);

    /**
     * 批量删除综合事项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizComprehensiveEventByIds(Long[] ids);

    /**
     * 统计所有综合事项数量
     * 
     * @return 综合事项总数
     */
    public int countAll();

    /**
     * 按日期范围查询办件趋势
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 办件趋势数据
     */
    List<Map<String, Object>> selectEventTrendByDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );



    /**
     * 获取疑难问题总数
     *
     * @return 总数
     */
    public int countDifficultIssues(@Param("bigdataParam") BigdataParam bigdataParam);

    /**
     * 按来源系统分组统计疑难问题数量
     *
     * @return 来源系统 -> 数量 的映射
     */
    public List<Map<String, Object>> countBySourceSystem(@Param("bigdataParam") BigdataParam bigdataParam);
}
