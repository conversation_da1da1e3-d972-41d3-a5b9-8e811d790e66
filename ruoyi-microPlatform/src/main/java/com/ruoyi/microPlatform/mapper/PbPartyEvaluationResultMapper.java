package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.domain.PbPartyEvaluationResult;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 党建评价结果Mapper接口
 */
@Mapper
public interface PbPartyEvaluationResultMapper {
    /** 查询 */
    PbPartyEvaluationResult selectPbPartyEvaluationResultById(Long id);

    /** 唯一性校验（result_code唯一） */
    PbPartyEvaluationResult checkPbPartyEvaluationResultUnique(PbPartyEvaluationResult info);

    /** 列表 */
    List<PbPartyEvaluationResult> selectPbPartyEvaluationResultList(PbPartyEvaluationResult info);

    /** 新增 */
    int insertPbPartyEvaluationResult(PbPartyEvaluationResult info);

    /** 修改 */
    int updatePbPartyEvaluationResult(PbPartyEvaluationResult info);

    /** 删除 */
    int deletePbPartyEvaluationResultById(Long id);

    /** 批量删除 */
    int deletePbPartyEvaluationResultByIds(Long[] ids);

    /** 分组：按星级统计（指定批次） */
    List<CommonBaseCount> groupByStarLevel(@Param("evaluationBatch") String evaluationBatch);
    
    /** 分组：按星级和城乡类型统计（指定批次） */
    List<CommonBaseCount> groupByStarLevelAndTownType(@Param("evaluationBatch") String evaluationBatch);
}
