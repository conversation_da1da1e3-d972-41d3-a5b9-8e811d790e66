package com.ruoyi.microPlatform.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DataSource(DataSourceType.SLAVE)
public interface SlaveHomePageMapper {
    int countUmEvents(@Param("param") BigdataParam param);
    int countHotlineDetails(@Param("param") BigdataParam param);
    int countEnterpriseAppeals(@Param("param") BigdataParam param);
    int countEmergencyEvents(@Param("param") BigdataParam param);
    int countPetitionInfos(@Param("param") BigdataParam param);
}

