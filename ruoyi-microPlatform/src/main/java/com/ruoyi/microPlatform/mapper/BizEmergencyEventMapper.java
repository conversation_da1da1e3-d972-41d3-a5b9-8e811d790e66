package com.ruoyi.microPlatform.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.microPlatform.domain.BizEmergencyEvent;

/**
 * 应急突发事件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
public interface BizEmergencyEventMapper 
{
    /**
     * 查询应急突发事件
     * 
     * @param id 应急突发事件主键
     * @return 应急突发事件
     */
    public BizEmergencyEvent selectBizEmergencyEventById(Long id);

    /**
     * 查询应急突发事件列表
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 应急突发事件集合
     */
    public List<BizEmergencyEvent> selectBizEmergencyEventList(BizEmergencyEvent bizEmergencyEvent);

    /**
     * 新增应急突发事件
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 结果
     */
    public int insertBizEmergencyEvent(BizEmergencyEvent bizEmergencyEvent);

    /**
     * 修改应急突发事件
     * 
     * @param bizEmergencyEvent 应急突发事件
     * @return 结果
     */
    public int updateBizEmergencyEvent(BizEmergencyEvent bizEmergencyEvent);

    /**
     * 删除应急突发事件信息
     * 
     * @param id 应急突发事件主键
     * @return 结果
     */
    public int deleteBizEmergencyEventById(Long id);

    /**
     * 批量删除应急突发事件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBizEmergencyEventByIds(Long[] ids);

    /**
     * 根据部门ID和状态统计数量
     * 
     * @param deptId 部门ID
     * @param eventStatus 事件状态
     * @param acceptStatus 签收状态
     * @return 数量
     */
    public int countByDeptIdAndStatus(@Param("deptId") Long deptId, 
                                    @Param("eventStatus") Integer eventStatus, 
                                    @Param("acceptStatus") Integer acceptStatus);
                                    
    /**
     * 根据部门ID、事件状态、受理状态和流程状态统计数量
     *
     * @param deptId 部门ID
     * @param eventStatus 事件状态
     * @param acceptStatus 受理状态
     * @param flowStatuses 流程状态数组
     * @return 数量
     */
    public int countByDeptIdAndFlowStatusIn(@Param("deptId") Long deptId,
                                          @Param("eventStatus") Integer eventStatus,
                                          @Param("acceptStatus") Integer acceptStatus,
                                          @Param("flowStatuses") Integer[] flowStatuses);
}
