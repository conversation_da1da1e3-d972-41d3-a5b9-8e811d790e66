package com.ruoyi.microPlatform.mapper;


import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface HomePageMapper {
    int countComprehensiveEvents(@Param("param") BigdataParam param);
    int countMajorOpinionEvents(@Param("param") BigdataParam param);

    /**
     * 获取督办统计信息
     * @param param 查询参数
     * @return 统计结果列表，包含总数量、已完成数量和满意数量
     */
    List<CommonBaseCount> selectSuperviseStats(@Param("param") BigdataParam param);
}