package com.ruoyi.microPlatform.bigdata.res;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class NowTianQiDto {
    /**
     * {"cityid":"101221401","date":"2025-09-12","week":"星期五","update_time":"21:38","city":"宣城","cityEn":"xuancheng","country":"中国","countryEn":"China","wea":"多云","wea_img":"yun","tem":"29.7","tem1":"34","tem2":"23","win":"西北风","win_speed":"2级","win_meter":"1km\/h","humidity":"82%","visibility":"27km","pressure":"1005","air":"22","air_pm25":"13","air_level":"优","air_tips":"空气质量令人满意，基本无空气污染。","alarm":[],"rain_pcpn":"6.1","uvIndex":"0","uvDescription":"低","wea_day":"阴","wea_day_img":"yin","wea_night":"小雨","wea_night_img":"yu","sunrise":"05:47","sunset":"18:15","hours":[{"hours":"21:00","wea":"小雨","wea_img":"yu","tem":"30","win":"西北风","win_speed":"1级","vis":"8.44","aqinum":"27","aqi":"优"},{"hours":"22:00","wea":"小雨","wea_img":"yu","tem":"29","win":"西北风","win_speed":"2级","vis":"6.82","aqinum":"25","aqi":"优"},{"hours":"23:00","wea":"大雨","wea_img":"yu","tem":"28","win":"西北风","win_speed":"2级","vis":"7.55","aqinum":"23","aqi":"优"},{"hours":"00:00","wea":"多云","wea_img":"yun","tem":"28","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"21","aqi":"优"},{"hours":"01:00","wea":"小雨","wea_img":"yu","tem":"27","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"20","aqi":"优"},{"hours":"02:00","wea":"小雨","wea_img":"yu","tem":"26","win":"西北风","win_speed":"2级","vis":"8.13","aqinum":"20","aqi":"优"},{"hours":"03:00","wea":"阴","wea_img":"yin","tem":"26","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"20","aqi":"优"},{"hours":"04:00","wea":"大雨","wea_img":"yu","tem":"25","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"20","aqi":"优"},{"hours":"05:00","wea":"多云","wea_img":"yun","tem":"25","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"21","aqi":"优"},{"hours":"06:00","wea":"小雨","wea_img":"yu","tem":"24","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"21","aqi":"优"},{"hours":"07:00","wea":"多云","wea_img":"yun","tem":"24","win":"西北风","win_speed":"2级","vis":"8.12","aqinum":"22","aqi":"优"},{"hours":"08:00","wea":"多云","wea_img":"yun","tem":"25","win":"西北风","win_speed":"2级","vis":"8.49","aqinum":"22","aqi":"优"},{"hours":"09:00","wea":"阴","wea_img":"yin","tem":"25","win":"西北风","win_speed":"2级","vis":"9.17","aqinum":"23","aqi":"优"},{"hours":"10:00","wea":"多云","wea_img":"yun","tem":"25","win":"西北风","win_speed":"2级","vis":"9.35","aqinum":"24","aqi":"优"},{"hours":"11:00","wea":"多云","wea_img":"yun","tem":"26","win":"西北风","win_speed":"2级","vis":"9.97","aqinum":"24","aqi":"优"},{"hours":"12:00","wea":"多云","wea_img":"yun","tem":"26","win":"西北风","win_speed":"2级","vis":"10.53","aqinum":"24","aqi":"优"},{"hours":"13:00","wea":"小雨","wea_img":"yu","tem":"27","win":"西北风","win_speed":"2级","vis":"10.85","aqinum":"23","aqi":"优"},{"hours":"14:00","wea":"小雨","wea_img":"yu","tem":"28","win":"西北风","win_speed":"2级","vis":"10.59","aqinum":"22","aqi":"优"},{"hours":"15:00","wea":"多云","wea_img":"yun","tem":"28","win":"西北风","win_speed":"2级","vis":"11.56","aqinum":"22","aqi":"优"},{"hours":"16:00","wea":"多云","wea_img":"yun","tem":"28","win":"西北风","win_speed":"2级","vis":"12.43","aqinum":"23","aqi":"优"},{"hours":"17:00","wea":"晴","wea_img":"qing","tem":"27","win":"北风","win_speed":"2级","vis":"12.26","aqinum":"22","aqi":"优"},{"hours":"18:00","wea":"晴","wea_img":"qing","tem":"27","win":"北风","win_speed":"2级","vis":"11.95","aqinum":"24","aqi":"优"},{"hours":"19:00","wea":"晴","wea_img":"qing","tem":"28","win":"北风","win_speed":"1级","vis":"11.24","aqinum":"26","aqi":"优"},{"hours":"20:00","wea":"晴","wea_img":"qing","tem":"27","win":"东北风","win_speed":"1级","vis":"10.49","aqinum":"27","aqi":"优"},{"hours":"21:00","wea":"多云","wea_img":"yun","tem":"27","win":"东风","win_speed":"1级","vis":"9.61","aqinum":"27","aqi":"优"},{"hours":"22:00","wea":"阴","wea_img":"yin","tem":"26","win":"东北风","win_speed":"1级","vis":"8.37","aqinum":"28","aqi":"优"}],"aqi":{"update_time":"21:40","cityid":"101221401","city":"宣城市","cityEn":"","country":"","countryEn":"","air":"22","air_level":"优","air_tips":"空气质量令人满意，基本无空气污染。","pm25":"13","pm25_desc":"优","pm10":"22","pm10_desc":"优","o3":"62","o3_desc":"","no2":"12","no2_desc":"","so2":"6","so2_desc":"","co":"0.4","co_desc":"","kouzhao":"不用佩戴口罩","yundong":"适宜运动","waichu":"适宜外出","kaichuang":"适宜开窗","jinghuaqi":"不需要打开"},"nums":0}
     */
    private String cityid;
    private Date date;
    private String week;
    private String update_time;
    private String city;
    private String cityEn;
    private String country;
    private String countryEn;
    private String wea;
    private String wea_img;
    private String tem;
    private String tem1;
    private String tem2;
    private String win;
    private String win_speed;
    private String win_meter;
    private String humidity;
    private String visibility;
    private String pressure;
    private String air;
    private String air_pm25;
    private String air_level;
    private String air_tips;
    private List<String> alarm;
    private String rain_pcpn;
    private String uvIndex;
    private String uvDescription;
    private String wea_day;
    private String wea_day_img;
    private String wea_night;
    private String wea_night_img;
    private String sunrise;
    private String sunset;
    private List<Hours> hours;
    private Aqi aqi;
    private int nums;


    @Data
    public static class Hours {

        private String hours;
        private String wea;
        private String wea_img;
        private String tem;
        private String win;
        private String win_speed;
        private String vis;
        private String aqinum;
        private String aqi;
    }

    @Data
    public static class Aqi {
        private String update_time;
        private String cityid;
        private String city;
        private String cityEn;
        private String country;
        private String countryEn;
        private String air;
        private String air_level;
        private String air_tips;
        private String pm25;
        private String pm25_desc;
        private String pm10;
        private String pm10_desc;
        private String o3;
        private String o3_desc;
        private String no2;
        private String no2_desc;
        private String so2;
        private String so2_desc;
        private String co;
        private String co_desc;
        private String kouzhao;
        private String yundong;
        private String waichu;
        private String kaichuang;
        private String jinghuaqi;
    }

}
