package com.ruoyi.microPlatform.bigdata.param;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import lombok.Data;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class BigdataParam extends LargeScreenInfo {

    /**
     * 维度 1 日 2 周 3月 4年
     */
    private Integer dimension;

    private Integer year;

    /**
     * 搜索时间区间 时间戳
     */
    private Long startTimeLong;
    private Long endTimeLong;


    private Date startTimeBySql;
    private Date endTimeBySql;


    public void calculateDateRange() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;

        if (this.dimension == null) {
            this.dimension = 1; // Default to daily
        }

        switch (this.dimension) {
            case 1: // 日
                start = now.toLocalDate().atStartOfDay();
                break;
            case 2: // 周
                start = now.toLocalDate().with(DayOfWeek.MONDAY).atStartOfDay();
                break;
            case 3: // 月
                start = now.toLocalDate().withDayOfMonth(1).atStartOfDay();
                break;
            case 4: // 年
                start = now.toLocalDate().withDayOfYear(1).atStartOfDay();
                break;
            default:
                throw new IllegalArgumentException("不支持的维度: " + this.dimension);
        }

        this.startTimeBySql = Date.from(start.atZone(ZoneId.systemDefault()).toInstant());
        this.endTimeBySql = new Date();
    }
}
