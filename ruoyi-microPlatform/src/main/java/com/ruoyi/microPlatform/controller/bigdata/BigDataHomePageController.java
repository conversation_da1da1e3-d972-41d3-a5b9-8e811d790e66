package com.ruoyi.microPlatform.controller.bigdata;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.service.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 首页 大数据相关api
 */
@RestController
@RequestMapping("/bigData/homePage")
public class BigDataHomePageController extends BaseController {

    @Resource
    private HomePageDataService homePageDataService;

    /**
     * 事项总数
     */
    @GetMapping("/tb/issueCount")
    public AjaxResult issueCount(BigdataParam bigdataParam) {
        // 数据来源 （综合事项（biz_comprehensive_event） + db_um_event（运管服） + db_hotline_detail（12345热线） + db_enterprise_appeal（万人助企） + db_emergency_event（应急局事项） + 市长信箱 +db_petition_info（信访）+ + major_opinion_events（舆情事项））
        //  其中 db_um_event 、 db_hotline_detail 、 db_enterprise_appeal 、db_emergency_event 、db_petition_info   在  @DataSource(DataSourceType.SLAVE) 里面

        return success(homePageDataService.getIssueCounts(bigdataParam));

    }

    /**
     * 督办
     */
    @GetMapping("/tb/superviseCount")
    public AjaxResult superviseCount(BigdataParam bigdataParam) {
        return success(homePageDataService.getSuperviseCounts(bigdataParam));
    }

    @Resource
    private IBizComprehensiveEventService bizComprehensiveEventService;

    /**
     * 获取疑难问题统计信息（按来源系统分组）
     * 
     * @param bigdataParam 查询参数
     * @return 统计结果，包含总数和按来源系统分组的数量
     */
    @GetMapping("/tb/difficultIssueList")
    public AjaxResult difficultIssueList(BigdataParam bigdataParam) {
        if (bigdataParam.getDimension() == null){
            bigdataParam.setDimension(1);
        }
        return success(bizComprehensiveEventService.getDifficultIssueStats(bigdataParam));
    }

    /**
     * 每日要情
     */
    @Resource
    private IBizDailySubmissionService bizDailySubmissionService;


    /**
     * 每日要请分页列表
     */
    @GetMapping("/tb/bizDailySubmissionListPage")
    public TableDataInfo bizDailySubmissionListPage(BizDailySubmission bizDailySubmission) {
        startPage();
        List<BizDailySubmission> list = bizDailySubmissionService.selectBizDailySubmissionList(bizDailySubmission);
        return getDataTable(list);
    }

    @Resource
    private ITbResidentMemberService tbResidentMemberService;

    /**
     * 重点人群总数
     */
    @GetMapping("/tb/keyPopulationsCount")
    public AjaxResult keyPopulationsCount(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCount(tbResidentMember));
    }

    /**
     * 重点人群分类数量
     */
    @GetMapping("/tb/keyPopulationsCountGroupByType")
    public AjaxResult keyPopulationsCountGroupByType(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCountGroupByType(tbResidentMember));
    }


    @Resource
    private ILargeScreenService largeScreenService;

    /**
     * 干部岗位类型数量
     */
    @GetMapping("/getDeptUserPostType")
    public AjaxResult getDeptUserPostType(LargeScreenInfo largeScreenInfo) {
//        handle(largeScreenInfo);
        Map<String, Integer> map = largeScreenService.getDeptUserPostType(largeScreenInfo);
        return success(map);
    }


    /**
     * 办件数量趋势
     */
    @GetMapping("/getBizComprehensiveEventTrend")
    public AjaxResult getBizComprehensiveEventTrend(BigdataParam bigdataParam) {
        return success(bizComprehensiveEventService.getBizComprehensiveEventTrend());
    }

    /**
     * 热门事项top6
     */
    @GetMapping("/getTop6EventMajors")
    public AjaxResult getTop6EventMajors(BigdataParam bigdataParam) {
        if (bigdataParam.getDimension() == null) {
            bigdataParam.setDimension(1);
        }
        return success(bizComprehensiveEventService.getTop6EventMajors(bigdataParam));
    }

}
