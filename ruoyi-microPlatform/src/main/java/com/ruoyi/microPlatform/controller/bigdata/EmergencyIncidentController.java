package com.ruoyi.microPlatform.controller.bigdata;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.BizDailySubmission;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.MajorOpinionEvent;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首页 大数据相关api
 */
@RestController
@RequestMapping("/bigData/emergencyIncident")
public class EmergencyIncidentController extends BaseController {


    @Resource
    private IMajorOpinionEventService majorOpinionEventService;

    /**
     * 舆情事件分页列表
     */
    @GetMapping("/majorOpinionEvent/listPage")
    public TableDataInfo majorOpinionEventListPage(MajorOpinionEvent majorOpinionEvent) {
        startPage();
        List<MajorOpinionEvent> list = majorOpinionEventService.selectMajorOpinionEventList(majorOpinionEvent);
        return getDataTable(list);
    }

    /**
     * 舆情事件 前7天统计
     * 
     * @return 7天内的舆情事件统计，包含日期和数量
     */
    @GetMapping("/majorOpinionEvent/last7DaysCount")
    public AjaxResult getMajorOpinionEventLast7DaysCount() {
        // 获取当前日期
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6); // 获取7天前的日期（包含今天）
        
        // 设置查询参数
        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        // 查询这7天内的舆情事件统计
        List<Map<String, Object>> eventCounts = majorOpinionEventService.countByDateRange(start, end);
        
        // 将查询结果转换为按日期映射
        Map<String, Long> dateCountMap = eventCounts.stream()
            .collect(Collectors.toMap(
                map -> (String) map.get("date"),
                map -> (Long) map.get("count")
            ));
        
        // 生成7天的日期列表，确保包含所有日期（包括没有数据的日期）
        List<Map<String, Object>> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            LocalDate date = startDate.plusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Long count = dateCountMap.getOrDefault(dateStr, 0L);
            
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", date.format(DateTimeFormatter.ofPattern("MM-dd")));
            dayData.put("count", count);
            result.add(dayData);
        }
        
        return success(result);
    }
}
