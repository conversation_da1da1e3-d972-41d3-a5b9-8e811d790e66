package com.ruoyi.microPlatform.controller.bigdata;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.microPlatform.bigdata.res.NowTianQiDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/bigData/tianQi")
public class TianQiController extends BaseController {

    private static final String nowTianQiUrl = "https://gfeljm.tianqiapi.com/api?province=河南&city=许昌&unescape=1&version=v63&appid=55993353&appsecret=6yqCGAVN";

    private static final String tianQiOf7DayUrl = "https://gfeljm.tianqiapi.com/api?province=河南&city=许昌&unescape=1&version=v91&appid=55993353&appsecret=6yqCGAVN";


    @Resource
    private RedisCache redisCache;

    /**
     * 实时天气
     */
    @RequestMapping("/getNowTianQi")
    public AjaxResult getNowTianQi() {
        // Try to get cached JSON string
        String cachedJson = redisCache.getCacheObject("BIGDATA:tianQi");
        if (cachedJson != null) {
            return success(JSONObject.parseObject(cachedJson, NowTianQiDto.class));
        }

        // If not in cache, fetch from API
        HttpRequest request = HttpUtil.createGet(nowTianQiUrl);
        request.setConnectionTimeout(5000);
        request.setReadTimeout(5000);
        HttpResponse execute = request.execute();
        if (execute.getStatus() != 200) {
            return error("请求天气接口失败");
        }
        String responseBody = execute.body();
        log.info("TianQiController.getNowTianQi: {}", responseBody);
        // Store the raw JSON string in Redis
        redisCache.setCacheObject("BIGDATA:tianQi", responseBody, 30, TimeUnit.MINUTES);

        // Parse and return the response
        return success(JSONObject.parseObject(responseBody, NowTianQiDto.class));
    }


    /**
     * 7tian
     */
    @RequestMapping("/getTianQiOf7Day")
    public AjaxResult getTianQiOf7Day() {
        // Try to get cached JSON string
        String cachedJson = redisCache.getCacheObject("BIGDATA:tianQiOf7Day");
        if (cachedJson != null) {
            return success(JSONObject.parseObject(cachedJson));
        }

        // If not in cache, fetch from API
        HttpRequest request = HttpUtil.createGet(tianQiOf7DayUrl);
        request.setConnectionTimeout(5000);
        request.setReadTimeout(5000);
        HttpResponse execute = request.execute();
        if (execute.getStatus() != 200) {
            return error("请求天气接口失败");
        }
        String responseBody = execute.body();
        log.info("TianQiController.getTianQiOf7Day: {}", responseBody);
        // Store the raw JSON string in Redis
        redisCache.setCacheObject("BIGDATA:tianQiOf7Day", responseBody, 30, TimeUnit.MINUTES);

        // Parse and return the response
        return success(JSONObject.parseObject(responseBody));
    }
}
