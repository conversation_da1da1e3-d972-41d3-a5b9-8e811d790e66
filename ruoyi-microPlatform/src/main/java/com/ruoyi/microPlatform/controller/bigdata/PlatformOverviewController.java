package com.ruoyi.microPlatform.controller.bigdata;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.controller.TbIssueInfoController;
import com.ruoyi.microPlatform.controller.bigdata.gov.GovBiz;
import com.ruoyi.microPlatform.controller.bigdata.gov.req.GovGetUnifiedManagementLetterListReq;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetLetterTypeModelResp;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetUnifiedManagementLetterListResp;
import com.ruoyi.microPlatform.domain.TbIssueInfo;
import com.ruoyi.microPlatform.service.*;
import com.ruoyi.microPlatform.service.IBizComprehensiveEventService;
import com.ruoyi.microPlatform.service.IBizDifficultIssueService;
import com.ruoyi.microPlatform.service.IBizEmergencyEventService;
import com.ruoyi.microPlatform.service.IBizWorkOrderTaskService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 常态事项
 */
@RestController
@RequestMapping("/bigData/platform")
public class PlatformOverviewController extends BaseController {
    @Resource
    private ISysDeptService sysDeptService;

    /**
     * 事项代办
     */
    @GetMapping("/homePage/tbIssueInfo")
    public AjaxResult tbIssueInfo(BigdataParam bigdataParam) {
        // 获取当前用户部门ID
        Long deptId = bigdataParam.getDeptId();
        if (deptId == null) {
            deptId = getDeptId();
        }

        SysDept sysDept = sysDeptService.selectDeptById(deptId);
        if (sysDept == null) {
            return error("部门不存在");
        }
        // 当 sysDept.getParentId 为 0 说明是市
        // 当 sysDept.getParentId 为 1 说明是区县
        try {
            if (sysDept.getParentId() == 0) {
                // 1. 本级代办
                // 1.1 biz_difficult_issue 表内status=0
                int localDifficultIssueCount = bizDifficultIssueService.countByStatus(0);

                // 1.2 biz_emergency_event 中accept_status=0并且当前处置单位id：current_dept_id是登录部门id
                int localEmergencyEventCount = bizEmergencyEventService.countByDeptIdAndStatus(deptId, null, 0);

                // 1.3 biz_work_order_task 表内handle_dept_id=当前用户登录部门id和task_status=0
                int localWorkOrderTaskCount = bizWorkOrderTaskService.countByHandleDeptIdAndStatusIn(deptId, new Integer[]{0});

                // 本级代办总数
                int localPendingCount = localDifficultIssueCount + localEmergencyEventCount + localWorkOrderTaskCount;

                // 2. 全市代办 市本级处理中
                // 2.1 biz_difficult_issue 中 status=1
                int processingDifficultIssueCount = bizDifficultIssueService.countByStatus(1);

                // 2.2 biz_emergency_event 中 event_status=0 and accept_status=1 and current_dept_id=登录部门id
                int processingEmergencyEventCount = bizEmergencyEventService.countByDeptIdAndStatus(deptId, 0, 1);

                // 2.3 biz_work_order_task 表内 handle_dept_id=当前用户deptId and (task_status=1 or task_status=7)
                int processingWorkOrderTaskCount = bizWorkOrderTaskService.countByHandleDeptIdAndStatusIn(deptId, new Integer[]{1, 7});

                // 全市代办总数
                int cityPendingCount = processingDifficultIssueCount + processingEmergencyEventCount + processingWorkOrderTaskCount;

                // 3. 全市事项总数 (biz_comprehensive_event数量)
                int totalEventCount = bizComprehensiveEventService.countAll();

                // 4. 督办事件数 (暂时返回0)
                int superviseEventCount = 0;

                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("localPendingCount", localPendingCount);
                result.put("cityPendingCount", cityPendingCount);
                result.put("totalEventCount", totalEventCount);
                result.put("superviseEventCount", superviseEventCount);

                return success(result);
            } else if (sysDept.getParentId() == 1) {
                // 1. 县级待办
                // 1.1 biz_emergency_event中accept_status=0并且当前处置单位id：current_dept_id是登录部门id
                int countyPendingEmergencyEventCount = bizEmergencyEventService.countByDeptIdAndStatus(deptId, null, 0);

                // 1.2 biz_work_order_task表内handle_dept_id=当前用户登录部门id和task_status=0
                int countyPendingWorkOrderTaskCount = bizWorkOrderTaskService.countByHandleDeptIdAndStatusIn(deptId, new Integer[]{0});

                // 县级待办总数
                int countyPendingCount = countyPendingEmergencyEventCount + countyPendingWorkOrderTaskCount;

                // 2. 县级处理中
                // 2.1 biz_emergency_event表内 event_status=0 and accept_status=1 and (flow_status=0 or flow_status=3) 
                //     并且当前处置单位id：current_dept_id=登录用户部门id
                int processingEmergencyEventCount = bizEmergencyEventService.countByDeptIdAndFlowStatusIn(deptId, 0, 1, new Integer[]{0, 3});

                // 2.2 biz_work_order_task 表内 handle_dept_id=用户deptId and (task_status=1 or task_status=7)
                int processingWorkOrderTaskCount = bizWorkOrderTaskService.countByHandleDeptIdAndStatusIn(deptId, new Integer[]{1, 7});

                // 县级处理中总数
                int countyProcessingCount = processingEmergencyEventCount + processingWorkOrderTaskCount;

                // 3. 全县事项总数 (biz_comprehensive_event数量)
                int totalEventCount = bizComprehensiveEventService.countAll();

                // 4. 督办事件数 (暂时返回0)
                int superviseEventCount = 0;

                // 构建返回结果
                Map<String, Object> result = new HashMap<>();
                result.put("localPendingCount", countyPendingCount);        // 县级待办
                result.put("cityPendingCount", countyProcessingCount);      // 县级处理中
                result.put("totalEventCount", totalEventCount);            // 全县事项总数
                result.put("superviseEventCount", superviseEventCount);    // 督办事件数

                return success(result);

            } else {
                return error("部门不存在");
            }

        } catch (Exception e) {
            logger.error("获取待办事项统计信息失败", e);
            return error("获取待办事项统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 矛盾纠纷
     */
    @Resource
    private ITbIssueInfoService tbIssueInfoService;

    @Resource
    private IBizComprehensiveEventService bizComprehensiveEventService;

    @Resource
    private IBizDifficultIssueService bizDifficultIssueService;

    @Resource
    private IBizEmergencyEventService bizEmergencyEventService;

    @Resource
    private IBizWorkOrderTaskService bizWorkOrderTaskService;

    /**
     * 矛盾纠纷 数量
     */
    @GetMapping("/homePage/tbIssueInfoCount")
    public AjaxResult tbIssueInfoCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(tbIssueInfoService.statusCount());
    }

    @Resource
    private TbIssueInfoController tbIssueInfoController;

    /**
     * 矛盾纠纷分页列表
     */
    @GetMapping("/homePage/tbIssueInfoListPage")
    public TableDataInfo tbIssueInfoListPage(TbIssueInfo tbIssueInfo) {
        return tbIssueInfoController.list(tbIssueInfo);
    }

    @Resource
    private ITbGridUserService tbGridUserService;

    /**
     * 九个县网格员
     */
    @GetMapping("/homePage/gridUserCount")
    public AjaxResult gridUserCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(tbGridUserService.gridUserCount());
    }


    @Resource
    private IMajorOpinionEventService majorOpinionEventService;

    /**
     * 热门事项
     * 改为舆情事项
     * 取 major_opinion_events 最新10条
     */
    @GetMapping("/homePage/majorOpinionEvents")
    public AjaxResult majorOpinionEvents(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(majorOpinionEventService.selectLatestOpinionEvents(10));
    }

    /**
     * 获取每个区域的舆情数量
     */
    @GetMapping("/homePage/letterArea")
    public AjaxResult letterArea(BigdataParam bigdataParam) {
        return success(majorOpinionEventService.letterArea());
    }




    @Resource
    private GovBiz govBiz;

    /**
     * 信件总数
     */
    @GetMapping("/homePage/letterCount")
    public AjaxResult letterCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(new GovGetUnifiedManagementLetterListReq());
        return success(unifiedManagementLetterList.getTotal());
    }

    /**
     * 信件类别
     * 这个只能循环查询 这样会很慢
     */
    @GetMapping("/homePage/letterType")
    public AjaxResult letterType(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        List<GovGetLetterTypeModelResp> letterTypeModel = govBiz.getLetterTypeModel();
        List<CommonBaseCount> res = new ArrayList<>();
        letterTypeModel.forEach(e -> {

            CommonBaseCount commonBaseCount = new CommonBaseCount();
            GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
            govGetUnifiedManagementLetterListReq.setType2(e.getValue());
            GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
            commonBaseCount.setLable(e.getText());
            commonBaseCount.setCount(unifiedManagementLetterList.getTotal());
            res.add(commonBaseCount);
        });
        return success(res);
    }


    /**
     * 信件状态
     * 这个只能循环查询 这样会很慢
     */
    @GetMapping("/homePage/letterStatus")
    public AjaxResult letterStatus(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        List<GovGetLetterTypeModelResp> letterTypeModel = govBiz.getStatusModel();
        List<CommonBaseCount> res = new ArrayList<>();
        letterTypeModel.forEach(e -> {
            CommonBaseCount commonBaseCount = new CommonBaseCount();
            GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
            govGetUnifiedManagementLetterListReq.setInfostatus(e.getValue());
            GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
            commonBaseCount.setLable(e.getText());
            commonBaseCount.setCount(unifiedManagementLetterList.getTotal());
            res.add(commonBaseCount);
        });
        return success(res);
    }

    @Resource
    private IXuLinMatterService xuLinMatterService;

    /**
     * 许邻e家事项统计
     *
     * @return 统计结果
     */
    @GetMapping("/xulinMatterStats")
    public AjaxResult getXuLinMatterStats(BigdataParam bigdataParam) {
        if (bigdataParam.getYear() == null) {
            bigdataParam.setYear(DateUtil.thisYear());
        }
        return success(xuLinMatterService.getXuLinMatterStats(bigdataParam));
    }
}
