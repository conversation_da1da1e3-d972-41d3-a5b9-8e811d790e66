<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.SubstationDataMapper">
    
    <resultMap type="SubstationData" id="SubstationDataResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="name"    column="name"    />
        <result property="m001t"    column="m001t"    />
        <result property="m002t"    column="m002t"    />
        <result property="m001p"    column="m001p"    />
        <result property="m002p"    column="m002p"    />
        <result property="m003q"    column="m003q"    />
        <result property="aligntime"    column="aligntime"    />
        <result property="type"    column="type"    />
        <result property="ppid"    column="ppid"    />
        <result property="childCount"    column="child_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSubstationDataVo">
        select id, pid, name, m001t, m002t, m001p, m002p, m003q, aligntime, type, ppid, child_count, create_by, create_time, update_by, update_time, remark from db_substation_data
    </sql>

    <select id="selectSubstationDataList" parameterType="SubstationData" resultMap="SubstationDataResult">
        <include refid="selectSubstationDataVo"/>
        <where>  
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="m001t != null "> and m001t = #{m001t}</if>
            <if test="m002t != null "> and m002t = #{m002t}</if>
            <if test="m001p != null "> and m001p = #{m001p}</if>
            <if test="m002p != null "> and m002p = #{m002p}</if>
            <if test="m003q != null "> and m003q = #{m003q}</if>
            <if test="aligntime != null "> and aligntime = #{aligntime}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="ppid != null "> and ppid = #{ppid}</if>
            <if test="childCount != null "> and child_count = #{childCount}</if>
        </where>
    </select>
    
    <select id="selectSubstationDataById" parameterType="Long" resultMap="SubstationDataResult">
        <include refid="selectSubstationDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSubstationData" parameterType="SubstationData" useGeneratedKeys="true" keyProperty="id">
        insert into db_substation_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="m001t != null">m001t,</if>
            <if test="m002t != null">m002t,</if>
            <if test="m001p != null">m001p,</if>
            <if test="m002p != null">m002p,</if>
            <if test="m003q != null">m003q,</if>
            <if test="aligntime != null">aligntime,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="ppid != null">ppid,</if>
            <if test="childCount != null">child_count,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="m001t != null">#{m001t},</if>
            <if test="m002t != null">#{m002t},</if>
            <if test="m001p != null">#{m001p},</if>
            <if test="m002p != null">#{m002p},</if>
            <if test="m003q != null">#{m003q},</if>
            <if test="aligntime != null">#{aligntime},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="ppid != null">#{ppid},</if>
            <if test="childCount != null">#{childCount},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertSubstationData" parameterType="java.util.List">
        insert into db_substation_data (pid, name, m001t, m002t, m001p, m002p, m003q, aligntime, type, ppid, child_count, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.pid},
                #{item.name},
                #{item.m001t},
                #{item.m002t},
                #{item.m001p},
                #{item.m002p},
                #{item.m003q},
                #{item.aligntime},
                #{item.type},
                #{item.ppid},
                #{item.childCount},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.remark}
            )
        </foreach>
    </insert>
    <insert id="batchInsertSiteData">
        insert into db_site_info (pid, site_name, type, lat, lng)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.pid},
                #{item.siteName},
                #{item.type},
                #{item.lat},
                #{item.lng}
            )
        </foreach>
    </insert>

    <update id="updateSubstationData" parameterType="SubstationData">
        update db_substation_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="m001t != null">m001t = #{m001t},</if>
            <if test="m002t != null">m002t = #{m002t},</if>
            <if test="m001p != null">m001p = #{m001p},</if>
            <if test="m002p != null">m002p = #{m002p},</if>
            <if test="m003q != null">m003q = #{m003q},</if>
            <if test="aligntime != null">aligntime = #{aligntime},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="ppid != null">ppid = #{ppid},</if>
            <if test="childCount != null">child_count = #{childCount},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSubstationDataById" parameterType="Long">
        delete from db_substation_data where id = #{id}
    </delete>

    <delete id="deleteSubstationDataByIds" parameterType="String">
        delete from db_substation_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="truncateSubstationData">
        truncate table db_substation_data
    </delete>

</mapper>