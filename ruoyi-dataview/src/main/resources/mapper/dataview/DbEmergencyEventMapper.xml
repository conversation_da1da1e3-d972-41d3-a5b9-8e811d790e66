<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbEmergencyEventMapper">
    
    <resultMap type="DbEmergencyEvent" id="DbEmergencyEventResult">
        <id     property="id"       column="id"        />
        <result property="eventCode"     column="event_code"      />
        <result property="eventTitle"    column="event_title"     />
        <result property="eventType"     column="event_type"      />
        <result property="eventSubtype"  column="event_subtype"   />
        <result property="eventLevel"    column="event_level"     />
        <result property="description"   column="description"     />
        <result property="occurTime"     column="occur_time"      />
        <result property="reportTime"    column="report_time"     />
        <result property="endTime"       column="end_time"        />
        <result property="location"      column="location"        />
        <result property="longitude"     column="longitude"       />
        <result property="latitude"      column="latitude"        />
        <result property="affectedArea"  column="affected_area"   />
        <result property="responseMeasure" column="response_measure" />
        <result property="casualties"    column="casualties"      />
        <result property="economicLoss"  column="economicLoss"    />
        <result property="status"        column="status"          />
        <result property="feedback"      column="feedback"        />
        <result property="createBy"      column="create_by"       />
        <result property="createTime"    column="create_time"     />
        <result property="updateBy"      column="update_by"       />
        <result property="updateTime"    column="update_time"     />
        <result property="deptId"        column="dept_id"         />
        <result property="county"        column="county"          />
        <result property="country"       column="country"         />
        <result property="town"          column="town"            />
    </resultMap>

    <sql id="selectDbEmergencyEventVo">
        select id, event_code, event_title, event_type, event_subtype, event_level, description, 
               occur_time, report_time, end_time, location, longitude, latitude, affected_area, 
               response_measure, casualties, economicLoss, status, feedback, create_by, 
               create_time, update_by, update_time, dept_id, county, country, town
        from db_emergency_event
    </sql>

    <select id="selectDbEmergencyEventList" parameterType="DbEmergencyEvent" resultMap="DbEmergencyEventResult">
        <include refid="selectDbEmergencyEventVo"/>
        <where>  
            <if test="eventCode != null  and eventCode != ''"> and event_code like concat('%', #{eventCode}, '%')</if>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="eventSubtype != null  and eventSubtype != ''"> and event_subtype like concat('%', #{eventSubtype}, '%')</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginOccurTime != null and params.beginOccurTime != ''"> and date_format(occur_time,'%y%m%d') &gt;= date_format(#{params.beginOccurTime},'%y%m%d')</if>
            <if test="params.endOccurTime != null and params.endOccurTime != ''"> and date_format(occur_time,'%y%m%d') &lt;= date_format(#{params.endOccurTime},'%y%m%d')</if>
        </where>
    </select>
    
    <select id="selectDbEmergencyEventById" parameterType="Long" resultMap="DbEmergencyEventResult">
        <include refid="selectDbEmergencyEventVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDbEmergencyEvent" parameterType="DbEmergencyEvent" useGeneratedKeys="true" keyProperty="id">
        insert into db_emergency_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventCode != null">event_code,</if>
            <if test="eventTitle != null">event_title,</if>
            <if test="eventType != null">event_type,</if>
            <if test="eventSubtype != null">event_subtype,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="description != null">description,</if>
            <if test="occurTime != null">occur_time,</if>
            <if test="reportTime != null">report_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null">location,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="affectedArea != null">affected_area,</if>
            <if test="responseMeasure != null">response_measure,</if>
            <if test="casualties != null">casualties,</if>
            <if test="economicLoss != null">economicLoss,</if>
            <if test="status != null">status,</if>
            <if test="feedback != null">feedback,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventCode != null">#{eventCode},</if>
            <if test="eventTitle != null">#{eventTitle},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="eventSubtype != null">#{eventSubtype},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="description != null">#{description},</if>
            <if test="occurTime != null">#{occurTime},</if>
            <if test="reportTime != null">#{reportTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null">#{location},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="affectedArea != null">#{affectedArea},</if>
            <if test="responseMeasure != null">#{responseMeasure},</if>
            <if test="casualties != null">#{casualties},</if>
            <if test="economicLoss != null">#{economicLoss},</if>
            <if test="status != null">#{status},</if>
            <if test="feedback != null">#{feedback},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            sysdate(),
            sysdate()
         </trim>
    </insert>

    <update id="updateDbEmergencyEvent" parameterType="DbEmergencyEvent">
        update db_emergency_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventCode != null">event_code = #{eventCode},</if>
            <if test="eventTitle != null">event_title = #{eventTitle},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="eventSubtype != null">event_subtype = #{eventSubtype},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="description != null">description = #{description},</if>
            <if test="occurTime != null">occur_time = #{occurTime},</if>
            <if test="reportTime != null">report_time = #{reportTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="affectedArea != null">affected_area = #{affectedArea},</if>
            <if test="responseMeasure != null">response_measure = #{responseMeasure},</if>
            <if test="casualties != null">casualties = #{casualties},</if>
            <if test="economicLoss != null">economicLoss = #{economicLoss},</if>
            <if test="status != null">status = #{status},</if>
            <if test="feedback != null">feedback = #{feedback},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbEmergencyEventById" parameterType="Long">
        delete from db_emergency_event where id = #{id}
    </delete>

    <delete id="deleteDbEmergencyEventByIds" parameterType="String">
        delete from db_emergency_event where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>
