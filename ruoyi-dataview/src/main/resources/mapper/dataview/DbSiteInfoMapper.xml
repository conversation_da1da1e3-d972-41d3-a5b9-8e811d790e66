<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbSiteInfoMapper">
    
    <resultMap type="DbSiteInfo" id="DbSiteInfoResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="pid"    column="pid"    />
        <result property="siteName"    column="site_name"    />
        <result property="lat"    column="lat"    />
        <result property="lng"    column="lng"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectDbSiteInfoVo">
        select id, type, pid, site_name, lat, lng, status from db_site_info
    </sql>

    <select id="selectDbSiteInfoList" parameterType="DbSiteInfo" resultMap="DbSiteInfoResult">
        <include refid="selectDbSiteInfoVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectDbSiteInfoById" parameterType="Long" resultMap="DbSiteInfoResult">
        <include refid="selectDbSiteInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDbSiteInfo" parameterType="DbSiteInfo" useGeneratedKeys="true" keyProperty="id">
        insert into db_site_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="pid != null">pid,</if>
            <if test="siteName != null">site_name,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="pid != null">#{pid},</if>
            <if test="siteName != null">#{siteName},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateDbSiteInfo" parameterType="DbSiteInfo">
        update db_site_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbSiteInfoById" parameterType="Long">
        delete from db_site_info where id = #{id}
    </delete>

    <delete id="deleteDbSiteInfoByIds" parameterType="String">
        delete from db_site_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>
