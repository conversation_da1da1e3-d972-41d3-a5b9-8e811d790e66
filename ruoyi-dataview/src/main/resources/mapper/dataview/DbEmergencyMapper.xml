<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbEmergencyMapper">

    <select id="dbEmergencyExpertCount" resultType="java.lang.Integer">
        select count(*) from db_emergency_expert
    </select>
    <select id="dbEmergencyMaterialsCount" resultType="java.lang.Integer">
        select count(*)
        from db_emergency_materials
    </select>
    <select id="dbEmergencyRescueTeamCount" resultType="java.lang.Integer">
        select count(*)
        from db_emergency_rescue_team
    </select>
    <select id="dbEmergencyShelterCount" resultType="java.lang.Integer">
        select count(*)
        from db_emergency_shelter
    </select>
</mapper>