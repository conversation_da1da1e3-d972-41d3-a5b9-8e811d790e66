package com.ruoyi.dataview.mapper;

import com.ruoyi.dataview.domain.po.DbEmergencyEvent;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 应急管理事项Mapper接口
 */
@Mapper
public interface DbEmergencyEventMapper {
    /**
     * 查询应急管理事项
     */
    DbEmergencyEvent selectDbEmergencyEventById(Long id);

    /**
     * 查询应急管理事项列表
     */
    List<DbEmergencyEvent> selectDbEmergencyEventList(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 新增应急管理事项
     */
    int insertDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 修改应急管理事项
     */
    int updateDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 删除应急管理事项
     */
    int deleteDbEmergencyEventById(Long id);

    /**
     * 批量删除应急管理事项
     */
    int deleteDbEmergencyEventByIds(Long[] ids);
}
