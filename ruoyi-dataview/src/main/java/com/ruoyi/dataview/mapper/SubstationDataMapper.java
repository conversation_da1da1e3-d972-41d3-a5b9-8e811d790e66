package com.ruoyi.dataview.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.dataview.domain.SiteExcel;
import com.ruoyi.dataview.domain.SubstationData;

/**
 * 热力站数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Mapper
public interface SubstationDataMapper 
{
    /**
     * 查询热力站数据
     * 
     * @param id 热力站数据主键
     * @return 热力站数据
     */
    public SubstationData selectSubstationDataById(Long id);

    /**
     * 查询热力站数据列表
     * 
     * @param substationData 热力站数据
     * @return 热力站数据集合
     */
    public List<SubstationData> selectSubstationDataList(SubstationData substationData);

    /**
     * 新增热力站数据
     * 
     * @param substationData 热力站数据
     * @return 结果
     */
    public int insertSubstationData(SubstationData substationData);

    /**
     * 批量新增热力站数据
     * 
     * @param substationDataList 热力站数据列表
     * @return 结果
     */
    public int batchInsertSubstationData(List<SubstationData> substationDataList);

    public int batchInsertSiteData(List<SiteExcel> list);

    /**
     * 修改热力站数据
     * 
     * @param substationData 热力站数据
     * @return 结果
     */
    public int updateSubstationData(SubstationData substationData);

    /**
     * 删除热力站数据
     * 
     * @param id 热力站数据主键
     * @return 结果
     */
    public int deleteSubstationDataById(Long id);

    /**
     * 批量删除热力站数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSubstationDataByIds(Long[] ids);

    /**
     * 清空热力站数据表
     * 
     * @return 结果
     */
    public int truncateSubstationData();
}