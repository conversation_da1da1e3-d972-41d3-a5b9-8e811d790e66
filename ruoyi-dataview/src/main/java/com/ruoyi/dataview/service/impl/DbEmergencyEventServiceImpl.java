package com.ruoyi.dataview.service.impl;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.dataview.domain.po.DbEmergencyEvent;
import com.ruoyi.dataview.mapper.DbEmergencyEventMapper;
import com.ruoyi.dataview.service.IDbEmergencyEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 应急管理事项Service业务层处理
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbEmergencyEventServiceImpl implements IDbEmergencyEventService {
    @Autowired
    private DbEmergencyEventMapper dbEmergencyEventMapper;

    /**
     * 查询应急管理事项
     */
    @Override
    public DbEmergencyEvent selectDbEmergencyEventById(Long id) {
        return dbEmergencyEventMapper.selectDbEmergencyEventById(id);
    }

    /**
     * 查询应急管理事项列表
     */
    @Override
    public List<DbEmergencyEvent> selectDbEmergencyEventList(DbEmergencyEvent dbEmergencyEvent) {
        return dbEmergencyEventMapper.selectDbEmergencyEventList(dbEmergencyEvent);
    }

    /**
     * 新增应急管理事项
     */
    @Override
    @Transactional
    public int insertDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent) {
        dbEmergencyEvent.setCreateBy(SecurityUtils.getUsername());
        return dbEmergencyEventMapper.insertDbEmergencyEvent(dbEmergencyEvent);
    }

    /**
     * 修改应急管理事项
     */
    @Override
    @Transactional
    public int updateDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent) {
        dbEmergencyEvent.setUpdateBy(SecurityUtils.getUsername());
        return dbEmergencyEventMapper.updateDbEmergencyEvent(dbEmergencyEvent);
    }

    /**
     * 批量删除应急管理事项
     */
    @Override
    @Transactional
    public int deleteDbEmergencyEventByIds(Long[] ids) {
        return dbEmergencyEventMapper.deleteDbEmergencyEventByIds(ids);
    }

    /**
     * 删除应急管理事项信息
     */
    @Override
    @Transactional
    public int deleteDbEmergencyEventById(Long id) {
        return dbEmergencyEventMapper.deleteDbEmergencyEventById(id);
    }
}
