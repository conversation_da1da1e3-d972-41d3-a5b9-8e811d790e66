package com.ruoyi.dataview.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;
import com.ruoyi.dataview.mapper.DbBpmNodeJumpMapper;
import com.ruoyi.dataview.mapper.DbEmergencyMapper;
import com.ruoyi.dataview.service.IDbBpmNodeJumpService;
import com.ruoyi.dataview.service.IDbEmergencyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbEmergencyServiceImpl implements IDbEmergencyService {
    @Autowired
    private DbEmergencyMapper dbEmergencyMapper;

    @Override
    public Map<String, Integer> emergencyResource() {
        Map<String, Integer> emergencyResource = new HashMap<>();
        emergencyResource.put("dbEmergencyExpertCount", dbEmergencyMapper.dbEmergencyExpertCount());
        emergencyResource.put("dbEmergencyMaterialsCount", dbEmergencyMapper.dbEmergencyMaterialsCount());
        emergencyResource.put("dbEmergencyRescueTeamCount", dbEmergencyMapper.dbEmergencyRescueTeamCount());
        emergencyResource.put("dbEmergencyShelterCount", dbEmergencyMapper.dbEmergencyShelterCount());
        return emergencyResource;
    }
}
