package com.ruoyi.dataview.service;

import java.util.List;

import com.ruoyi.dataview.domain.SiteExcel;
import com.ruoyi.dataview.domain.SubstationData;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 热力站数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface ISubstationDataService 
{
    /**
     * 查询热力站数据列表
     * 
     * @param substationData 热力站数据
     * @return 热力站数据集合
     */
    public List<SubstationData> selectSubstationDataList(SubstationData substationData);

    /**
     * 根据PID查询热力站数据
     * 
     * @param pid 热力站ID
     * @return 热力站数据
     */
    public SubstationData selectSubstationDataByPid(Integer pid);

    /**
     * 刷新热力站数据（从外部接口获取最新数据）
     * 
     * @return 结果
     */
    public int refreshSubstationData();
    
    /**
     * 获取分页结果
     * 
     * @param allData 所有数据
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param criteria 查询条件
     * @return 分页结果
     */
    public TableDataInfo getPagedResult(List<SubstationData> allData, Integer pageNum, Integer pageSize, SubstationData criteria);

    /**
     * 获取指定主站点的所有子站点
     * 
     * @param parentPid 主站点ID
     * @return 子站点列表
     */
    public List<SubstationData> getSubstationsByParentId(Integer parentPid);

    /**
     * 清空热力站数据表
     * 
     * @return 结果
     */
    public int clearSubstationData();

    /**
     * 批量插入热力站数据到数据库
     * 
     * @param substationDataList 热力站数据列表
     * @return 结果
     */
    public int batchInsertSubstationData(List<SubstationData> substationDataList);

    /**
     * 将当前查询结果插入数据库（先清空再插入）
     * 
     * @return 结果
     */
    public int insertCurrentDataToDatabase();

    public String importSubstationData(List<SiteExcel> list);
}