package com.ruoyi.dataview.service;

import java.util.List;
import com.ruoyi.dataview.domain.DbSiteInfo;

/**
 * 热力站信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
public interface IDbSiteInfoService 
{
    /**
     * 查询热力站信息
     * 
     * @param id 热力站信息主键
     * @return 热力站信息
     */
    public DbSiteInfo selectDbSiteInfoById(Long id);

    /**
     * 查询热力站信息列表
     * 
     * @param dbSiteInfo 热力站信息
     * @return 热力站信息集合
     */
    public List<DbSiteInfo> selectDbSiteInfoList(DbSiteInfo dbSiteInfo);

    /**
     * 新增热力站信息
     * 
     * @param dbSiteInfo 热力站信息
     * @return 结果
     */
    public int insertDbSiteInfo(DbSiteInfo dbSiteInfo);

    /**
     * 修改热力站信息
     * 
     * @param dbSiteInfo 热力站信息
     * @return 结果
     */
    public int updateDbSiteInfo(DbSiteInfo dbSiteInfo);

    /**
     * 批量删除热力站信息
     * 
     * @param ids 需要删除的热力站信息主键集合
     * @return 结果
     */
    public int deleteDbSiteInfoByIds(Long[] ids);

    /**
     * 删除热力站信息信息
     * 
     * @param id 热力站信息主键
     * @return 结果
     */
    public int deleteDbSiteInfoById(Long id);
}
