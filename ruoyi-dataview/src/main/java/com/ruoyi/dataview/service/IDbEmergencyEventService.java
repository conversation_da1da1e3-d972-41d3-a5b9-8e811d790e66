package com.ruoyi.dataview.service;

import com.ruoyi.dataview.domain.po.DbEmergencyEvent;
import java.util.List;

/**
 * 应急管理事项Service接口
 */
public interface IDbEmergencyEventService {
    /**
     * 查询应急管理事项
     */
    DbEmergencyEvent selectDbEmergencyEventById(Long id);

    /**
     * 查询应急管理事项列表
     */
    List<DbEmergencyEvent> selectDbEmergencyEventList(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 新增应急管理事项
     */
    int insertDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 修改应急管理事项
     */
    int updateDbEmergencyEvent(DbEmergencyEvent dbEmergencyEvent);

    /**
     * 批量删除应急管理事项
     */
    int deleteDbEmergencyEventByIds(Long[] ids);

    /**
     * 删除应急管理事项信息
     */
    int deleteDbEmergencyEventById(Long id);
}
