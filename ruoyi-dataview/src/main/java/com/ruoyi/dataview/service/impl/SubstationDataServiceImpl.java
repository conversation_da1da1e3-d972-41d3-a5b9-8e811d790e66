package com.ruoyi.dataview.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.dataview.domain.SiteExcel;
import com.ruoyi.dataview.util.BaiduGeocoder;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.dataview.domain.SubstationData;
import com.ruoyi.dataview.mapper.SubstationDataMapper;
import com.ruoyi.dataview.service.ISubstationDataService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 热力站数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class SubstationDataServiceImpl implements ISubstationDataService 
{
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private SubstationDataMapper substationDataMapper;
    
    private static final String API_URL = "http://117.159.52.147:8686/digital-service/open/queryDb?token=control5a215adf341569f6d61d8193c&Modeltype=00";
    
    // 缓存数据，避免频繁调用外部接口
    private List<SubstationData> cachedData = new ArrayList<>();
    private long lastUpdateTime = 0;
    private static final long CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
    
    /**
     * 查询热力站数据列表
     * 
     * @param substationData 热力站数据
     * @return 热力站数据
     */
    @Override
    public List<SubstationData> selectSubstationDataList(SubstationData substationData)
    {
        // 检查缓存是否过期
        if (System.currentTimeMillis() - lastUpdateTime > CACHE_DURATION || cachedData.isEmpty()) {
            refreshSubstationData();
        }
        
        // 过滤有效数据（过滤出来是主站点的数据）
        List<SubstationData> validData = cachedData.stream()
            .filter(data -> isMainData(data))
            .collect(Collectors.toList());

        List<SubstationData> subData = cachedData.stream()
                .filter(data -> isSubData(data))
                .collect(Collectors.toList());
        
        // 为每个主站点计算子站点数量
        for (SubstationData mainStation : validData) {
            int childCount = (int) subData.stream()
                .filter(data -> data.getPpid().equals(mainStation.getPid()))// 再找出来子站点的ppid等于主站点id的数
                .count();
            mainStation.setChildCount(childCount);
        }
        
        // 根据查询条件过滤数据
        return validData.stream()
            .filter(data -> matchesSearchCriteria(data, substationData))
            .collect(Collectors.toList());
    }
    
    /**
     * 获取分页结果
     */
    @Override
    public TableDataInfo getPagedResult(List<SubstationData> allData, Integer pageNum, Integer pageSize, SubstationData criteria) {
        // 重新获取过滤后的数据
        List<SubstationData> filteredData = selectSubstationDataList(criteria);
        
        int total = filteredData.size();
        
        // 设置默认分页参数
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        
        // 计算分页范围
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);
        
        List<SubstationData> pageData;
        if (startIndex >= total) {
            pageData = new ArrayList<>();
        } else {
            pageData = filteredData.subList(startIndex, endIndex);
        }
        
        // 构建分页结果
        TableDataInfo result = new TableDataInfo();
        result.setRows(pageData);
        result.setTotal(total);
        result.setCode(200);
        result.setMsg("查询成功");
        
        return result;
    }
    
    /**
     * 验证数据是否是主站点（关键字段不为空且为主站点）
     */
    private boolean isMainData(SubstationData data) {
        return data.getPid() != null &&
               data.getPpid() != null &&
               data.getPid().equals(data.getPpid()); // 主站点：pid和ppid相同
    }

    /**
     * 清空热力站数据表
     */
    @Override
    public int clearSubstationData() {
        return substationDataMapper.truncateSubstationData();
    }

    /**
     * 批量插入热力站数据到数据库
     */
    @Override
    public int batchInsertSubstationData(List<SubstationData> substationDataList) {
        if (substationDataList == null || substationDataList.isEmpty()) {
            return 0;
        }
        
        // 设置创建信息
        String currentUser = SecurityUtils.getUsername();
        Date currentTime = new Date();
        
        for (SubstationData data : substationDataList) {
            data.setCreateBy(currentUser);
            data.setCreateTime(currentTime);
            data.setUpdateBy(currentUser);
            data.setUpdateTime(currentTime);
        }
        
        return substationDataMapper.batchInsertSubstationData(substationDataList);
    }

    /**
     * 将当前查询结果插入数据库（先清空再插入）
     */
    @Override
    public int insertCurrentDataToDatabase() {
        // 获取当前缓存的数据
        List<SubstationData> currentData = cachedData;
        
        if (currentData == null || currentData.isEmpty()) {
            return 0;
        }
        
        // 先清空数据表
        clearSubstationData();
        
        // 批量插入当前数据
        return batchInsertSubstationData(currentData);
    }

    /**
     * 数据是否为子站点（关键字段不为空且为子站点）
     */
    private boolean isSubData(SubstationData data) {
        return data.getPid() == null &&
               data.getPpid() != null;
    }

    /**
     * 检查数据是否匹配搜索条件
     */
    private boolean matchesSearchCriteria(SubstationData data, SubstationData criteria) {
        if (criteria.getPid() != null && !criteria.getPid().equals(data.getPid())) {
            return false;
        }
        if (criteria.getName() != null && !criteria.getName().isEmpty() 
            && (data.getName() == null || !data.getName().contains(criteria.getName()))) {
            return false;
        }
        if (criteria.getType() != null && !criteria.getType().isEmpty() 
            && !criteria.getType().equals(data.getType())) {
            return false;
        }
        // 支持按子站点数量筛选：childCount为0表示筛选无子站点的主站点，大于0表示筛选有子站点的主站点
        if (criteria.getChildCount() != null) {
            if (criteria.getChildCount() == 0 && (data.getChildCount() == null || data.getChildCount() > 0)) {
                return false;
            }
            if (criteria.getChildCount() > 0 && (data.getChildCount() == null || data.getChildCount() == 0)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String importSubstationData(List<SiteExcel> list) {
        if (!list.isEmpty()){
            list.forEach(item ->{
                item.setType("热力站");
                try {
                    String geoCode = BaiduGeocoder.getGeoCode(item.getSiteName());
                    if (StringUtils.isNotBlank(geoCode)){
                        String[] split = geoCode.split(",");
                        if (split.length == 2){
                            item.setLng(new BigDecimal(split[0]));
                            item.setLat(new BigDecimal(split[1]));
                        }
                    }
                    Thread.sleep(1500); // 延时200毫秒，根据你的配额调整
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            substationDataMapper.batchInsertSiteData(list);
        }
        return "";
    }

    /**
     * 根据PID查询热力站数据
     * 
     * @param pid 热力站ID
     * @return 热力站数据
     */
    @Override
    public SubstationData selectSubstationDataByPid(Integer pid)
    {
        // 检查缓存是否过期
        if (System.currentTimeMillis() - lastUpdateTime > CACHE_DURATION || cachedData.isEmpty()) {
            refreshSubstationData();
        }
        
        for (SubstationData data : cachedData) {
            if (data.getPid().equals(pid)) {
                // 设置子站点列表
                data.setChildren(cachedData.stream().filter(item -> isSubData(item) && item.getPpid().equals(data.getPid())).collect(Collectors.toList()));
                return data;
            }
        }
        return null;
    }

    /**
     * 刷新热力站数据（从外部接口获取最新数据）
     * 
     * @return 结果
     */
    @Override
    public int refreshSubstationData()
    {
        try {
            // 构建请求参数
            String requestBody = "{"
                + "\"queries\": ["
                + "{"
                + "\"metric\": \"pid,name,m_001t,m_002t,m_001p,m_002p,m_003q\","
                + "\"schema\": \"monitor\""
                + "}"
                + "],"
                + "\"orderBy\": \"pid asc\","
                + "\"table\": \"substation\""
                + "}";
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                API_URL, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析响应数据
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> responseMap = mapper.readValue(
                    response.getBody(), new TypeReference<Map<String, Object>>() {});
                
                if ((Integer) responseMap.get("code") == 200) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseMap.get("data");
                    
                    List<SubstationData> newData = new ArrayList<>();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    
                    for (Map<String, Object> item : dataList) {
                        SubstationData data = new SubstationData();
                        data.setPid((Integer) item.get("pid"));
                        data.setName((String) item.get("name"));
                        data.setType((String) item.get("type"));
                        data.setPpid((Integer) item.get("ppid"));
                        
                        // 处理数值类型
                        if (item.get("m_001t") != null) {
                            data.setM001t(new BigDecimal(item.get("m_001t").toString()));
                        }
                        if (item.get("m_002t") != null) {
                            data.setM002t(new BigDecimal(item.get("m_002t").toString()));
                        }
                        if (item.get("m_001p") != null) {
                            data.setM001p(new BigDecimal(item.get("m_001p").toString()));
                        }
                        if (item.get("m_002p") != null) {
                            data.setM002p(new BigDecimal(item.get("m_002p").toString()));
                        }
                        if (item.get("m_003q") != null) {
                            data.setM003q(new BigDecimal(item.get("m_003q").toString()));
                        }
                        
                        // 处理时间
                        if (item.get("aligntime") != null) {
                            try {
                                data.setAligntime(sdf.parse((String) item.get("aligntime")));
                            } catch (ParseException e) {
                                data.setAligntime(new Date());
                            }
                        }
                        
                        newData.add(data);
                    }
                    
                    // 更新缓存
                    cachedData = newData;
                    lastUpdateTime = System.currentTimeMillis();
                    
                    return newData.size();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return 0;
    }

    /**
     * 获取指定主站点的所有子站点
     */
    @Override
    public List<SubstationData> getSubstationsByParentId(Integer parentPid) {
        // 检查缓存是否过期
        if (System.currentTimeMillis() - lastUpdateTime > CACHE_DURATION || cachedData.isEmpty()) {
            refreshSubstationData();
        }
        
        return cachedData.stream()
                .filter(data -> data.getPid() == null && data.getPpid() != null && data.getPpid().equals(parentPid)) // 子站点：pid为空，ppid不为空
                .filter(data -> data.getName() != null && !data.getName().trim().isEmpty())
                .collect(Collectors.toList());
    }
}