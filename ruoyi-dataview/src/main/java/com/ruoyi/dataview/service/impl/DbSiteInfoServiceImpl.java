package com.ruoyi.dataview.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbSiteInfoMapper;
import com.ruoyi.dataview.domain.DbSiteInfo;
import com.ruoyi.dataview.service.IDbSiteInfoService;

/**
 * 热力站信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Service
public class DbSiteInfoServiceImpl implements IDbSiteInfoService 
{
    @Autowired
    private DbSiteInfoMapper dbSiteInfoMapper;

    /**
     * 查询热力站信息
     * 
     * @param id 热力站信息主键
     * @return 热力站信息
     */
    @Override
    public DbSiteInfo selectDbSiteInfoById(Long id)
    {
        return dbSiteInfoMapper.selectDbSiteInfoById(id);
    }

    /**
     * 查询热力站信息列表
     * 
     * @param dbSiteInfo 热力站信息
     * @return 热力站信息
     */
    @Override
    public List<DbSiteInfo> selectDbSiteInfoList(DbSiteInfo dbSiteInfo)
    {
        return dbSiteInfoMapper.selectDbSiteInfoList(dbSiteInfo);
    }

    /**
     * 新增热力站信息
     * 
     * @param dbSiteInfo 热力站信息
     * @return 结果
     */
    @Override
    public int insertDbSiteInfo(DbSiteInfo dbSiteInfo)
    {
        return dbSiteInfoMapper.insertDbSiteInfo(dbSiteInfo);
    }

    /**
     * 修改热力站信息
     * 
     * @param dbSiteInfo 热力站信息
     * @return 结果
     */
    @Override
    public int updateDbSiteInfo(DbSiteInfo dbSiteInfo)
    {
        return dbSiteInfoMapper.updateDbSiteInfo(dbSiteInfo);
    }

    /**
     * 批量删除热力站信息
     * 
     * @param ids 需要删除的热力站信息主键集合
     * @return 结果
     */
    @Override
    public int deleteDbSiteInfoByIds(Long[] ids)
    {
        return dbSiteInfoMapper.deleteDbSiteInfoByIds(ids);
    }

    /**
     * 删除热力站信息信息
     * 
     * @param id 热力站信息主键
     * @return 结果
     */
    @Override
    public int deleteDbSiteInfoById(Long id)
    {
        return dbSiteInfoMapper.deleteDbSiteInfoById(id);
    }
}
