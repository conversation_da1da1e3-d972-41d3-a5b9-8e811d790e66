package com.ruoyi.dataview.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.dataview.domain.SiteExcel;
import com.ruoyi.dataview.domain.po.DbWmSite;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.SubstationData;
import com.ruoyi.dataview.service.ISubstationDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import org.springframework.web.multipart.MultipartFile;

/**
 * 热力站数据Controller
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/dataview/substationData")
public class SubstationDataController extends BaseController
{
    @Autowired
    private ISubstationDataService substationDataService;

    /**
     * 查询热力站数据列表
     * 支持通过childCount参数筛选：
     * - childCount=1: 筛选有子站点的主站点
     * - childCount=0: 筛选无子站点的主站点
     * - childCount为空: 显示所有主站点
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:list')")
    @GetMapping("/list")
    public TableDataInfo list(SubstationData substationData)
    {
        // 由于数据来自外部API，需要在Service层手动处理分页
        // 这里不调用startPage()，而是将分页参数传递给Service
        List<SubstationData> list = substationDataService.selectSubstationDataList(substationData);
        
        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        
        // 手动构建分页结果
        return substationDataService.getPagedResult(list, pageNum, pageSize, substationData);
    }

    /**
     * 清空热力站数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:remove')")
    @DeleteMapping("/clear")
    public AjaxResult clearData()
    {
        int result = substationDataService.clearSubstationData();
        return result > 0 ? AjaxResult.success("清空数据成功") : AjaxResult.error("清空数据失败");
    }

    /**
     * 将当前查询结果插入数据库
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:add')")
    @PostMapping("/insertCurrent")
    public AjaxResult insertCurrentData()
    {
        int result = substationDataService.insertCurrentDataToDatabase();
        return result > 0 ? AjaxResult.success("插入数据成功，共插入 " + result + " 条记录") : AjaxResult.error("插入数据失败");
    }

    /**
     * 导出热力站数据列表
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:export')")
    @Log(title = "热力站数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SubstationData substationData)
    {
        List<SubstationData> list = substationDataService.selectSubstationDataList(substationData);
        ExcelUtil<SubstationData> util = new ExcelUtil<SubstationData>(SubstationData.class);
        util.exportExcel(response, list, "热力站数据数据");
    }

    /**
     * 获取指定主站点的所有子站点
     */
    @GetMapping("/substations/{parentPid}")
    public AjaxResult getSubstations(@PathVariable("parentPid") Integer parentPid)
    {
        List<SubstationData> substations = substationDataService.getSubstationsByParentId(parentPid);
        return AjaxResult.success(substations);
    }

    /**
     * 筛选有子站点的主站点
     */
    @GetMapping("/withChildren")
    public TableDataInfo getMainStationsWithChildren(SubstationData substationData)
    {
        // 设置筛选条件：有子站点
        substationData.setChildCount(1); // 大于0表示有子站点
        
        List<SubstationData> list = substationDataService.selectSubstationDataList(substationData);
        
        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        
        return substationDataService.getPagedResult(list, pageNum, pageSize, substationData);
    }

    /**
     * 筛选无子站点的主站点
     */
    @GetMapping("/withoutChildren")
    public TableDataInfo getMainStationsWithoutChildren(SubstationData substationData)
    {
        // 设置筛选条件：无子站点
        substationData.setChildCount(0); // 等于0表示无子站点
        
        List<SubstationData> list = substationDataService.selectSubstationDataList(substationData);
        
        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        
        return substationDataService.getPagedResult(list, pageNum, pageSize, substationData);
    }

    /**
     * 获取热力站数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:query')")
    @GetMapping(value = "/{pid}")
    public AjaxResult getInfo(@PathVariable("pid") Integer pid)
    {
        return success(substationDataService.selectSubstationDataByPid(pid));
    }

    /**
     * 刷新热力站数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:substationData:refresh')")
    @Log(title = "热力站数据", businessType = BusinessType.OTHER)
    @PostMapping("/refresh")
    public AjaxResult refresh()
    {
        int count = substationDataService.refreshSubstationData();
        return success("刷新成功，共获取 " + count + " 条数据");
    }


    @PostMapping("/importData")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SiteExcel> util = new ExcelUtil<SiteExcel>(SiteExcel.class);
        List<SiteExcel> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = substationDataService.importSubstationData(infos);
        return success(message);
    }
}