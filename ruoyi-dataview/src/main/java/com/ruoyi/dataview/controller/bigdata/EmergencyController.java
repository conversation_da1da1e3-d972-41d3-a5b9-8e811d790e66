package com.ruoyi.dataview.controller.bigdata;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbEmergencyEvent;
import com.ruoyi.dataview.domain.po.DbSiteData;
import com.ruoyi.dataview.domain.po.DbWmSite;
import com.ruoyi.dataview.service.IDbEmergencyEventService;
import com.ruoyi.dataview.service.IDbEmergencyService;
import com.ruoyi.dataview.service.IDbSiteDataService;
import com.ruoyi.dataview.service.IDbWmSiteService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 应急事件
 */
@RestController
@RequestMapping("/bigData/emergency")
public class EmergencyController extends BaseController {

    /**
     * 水位检测 记录
     */
    @Resource
    private IDbSiteDataService dbSiteDataService;

    /**
     * 水位检测设备
     */
    @Resource
    private IDbWmSiteService dbWmSiteService;


    /**
     * 水位检测 分页列表
     */
    @GetMapping("/db/siteDatalistPage")
    public TableDataInfo siteDatalistPage(DbSiteData dbSiteData) {
        startPage();
        List<DbSiteData> list = dbSiteDataService.selectDbSiteDataList(dbSiteData);
        return getDataTable(list);
    }


    /**
     * 涵洞设备列表
     */
    @GetMapping("/db/wdDataListPage")
    public TableDataInfo wdDataListPage(DbWmSite dbSiteData) {
        startPage();
        List<DbWmSite> list = dbWmSiteService.selectDbWmSiteList(dbSiteData);
        return getDataTable(list);
    }


    /**
     * 水位检测 状态
     */
    @GetMapping("/db/wdDataStatusCount")
    public AjaxResult wdDataStatusCount(DbSiteData dbSiteData) {
        List<CommonBaseCount> list = dbWmSiteService.wdDataStatusCount();
        return success(list);
    }

    @Resource
    private IDbEmergencyService dbEmergencyService;

    /**
     * 应急资源
     */
    @GetMapping("/emergencyResource")
    public AjaxResult emergencyResource() {
        return success(dbEmergencyService.emergencyResource());
    }

    @Resource
    private IDbEmergencyEventService dbEmergencyEventService;

    /**
     * 应急事件分页
     */
    @GetMapping("/db/emergencyEventlistPage")
    public TableDataInfo emergencyEventlistPage(DbEmergencyEvent dbEmergencyEvent) {
        startPage();
        List<DbEmergencyEvent> list = dbEmergencyEventService.selectDbEmergencyEventList(dbEmergencyEvent);
        return getDataTable(list);
    }


}
