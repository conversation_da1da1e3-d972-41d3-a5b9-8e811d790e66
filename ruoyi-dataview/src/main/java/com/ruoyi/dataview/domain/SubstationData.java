package com.ruoyi.dataview.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 热力站数据对象 substation_data
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public class SubstationData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 热力站ID */
    @Excel(name = "热力站ID")
    private Integer pid;

    /** 热力站名称 */
    @Excel(name = "热力站名称")
    private String name;

    /** 一次侧供温 */
    @Excel(name = "一次侧供温")
    private BigDecimal m001t;

    /** 一次侧回温 */
    @Excel(name = "一次侧回温")
    private BigDecimal m002t;

    /** 一次侧供压 */
    @Excel(name = "一次侧供压")
    private BigDecimal m001p;

    /** 一次侧回压 */
    @Excel(name = "一次侧回压")
    private BigDecimal m002p;

    /** 一次侧流量 */
    @Excel(name = "一次侧流量")
    private BigDecimal m003q;

    /** 数据对齐时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据对齐时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date aligntime;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 父级ID */
    @Excel(name = "父级ID")
    private Integer ppid;

    /** 子站点数量 */
    @Excel(name = "子站点数量")
    private Integer childCount;

    private List<SubstationData> children;

    public List<SubstationData> getChildren() {
        return children;
    }

    public void setChildren(List<SubstationData> children) {
        this.children = children;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPid(Integer pid)
    {
        this.pid = pid;
    }

    public Integer getPid() 
    {
        return pid;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setM001t(BigDecimal m001t) 
    {
        this.m001t = m001t;
    }

    public BigDecimal getM001t() 
    {
        return m001t;
    }
    public void setM002t(BigDecimal m002t) 
    {
        this.m002t = m002t;
    }

    public BigDecimal getM002t() 
    {
        return m002t;
    }
    public void setM001p(BigDecimal m001p) 
    {
        this.m001p = m001p;
    }

    public BigDecimal getM001p() 
    {
        return m001p;
    }
    public void setM002p(BigDecimal m002p) 
    {
        this.m002p = m002p;
    }

    public BigDecimal getM002p() 
    {
        return m002p;
    }
    public void setM003q(BigDecimal m003q) 
    {
        this.m003q = m003q;
    }

    public BigDecimal getM003q() 
    {
        return m003q;
    }
    public void setAligntime(Date aligntime) 
    {
        this.aligntime = aligntime;
    }

    public Date getAligntime() 
    {
        return aligntime;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setPpid(Integer ppid) 
    {
        this.ppid = ppid;
    }

    public Integer getPpid() 
    {
        return ppid;
    }
    public void setChildCount(Integer childCount) 
    {
        this.childCount = childCount;
    }

    public Integer getChildCount() 
    {
        return childCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("name", getName())
            .append("m001t", getM001t())
            .append("m002t", getM002t())
            .append("m001p", getM001p())
            .append("m002p", getM002p())
            .append("m003q", getM003q())
            .append("aligntime", getAligntime())
            .append("type", getType())
            .append("ppid", getPpid())
            .append("childCount", getChildCount())
            .toString();
    }
}