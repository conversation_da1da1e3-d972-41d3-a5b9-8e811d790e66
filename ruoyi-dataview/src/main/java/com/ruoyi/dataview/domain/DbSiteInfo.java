package com.ruoyi.dataview.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 热力站信息对象 db_site_info
 * 
 * <AUTHOR>
 * @date 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DbSiteInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 热力站类型 */
    @Excel(name = "热力站类型")
    private String type;

    /** pid */
    @Excel(name = "pid")
    private Integer pid;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal lat;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal lng;

    /** 站点状态（1正常 0停用） */
    @Excel(name = "站点状态", readConverterExp = "1=正常,0=停用")
    private Integer status;
}
