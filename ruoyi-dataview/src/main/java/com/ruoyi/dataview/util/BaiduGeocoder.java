package com.ruoyi.dataview.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.util.UriUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 百度地图地理编码工具类
 */
public class BaiduGeocoder {

    // 你的百度地图AK (此处需要替换!!!)
    private static final String AK = "4RABRC7cPSTq50d0PcDWxHxt7JNtOXSX";
    // 地理编码API的URL
    private static final String GEOCODING_URL = "http://api.map.baidu.com/geocoding/v3/";

    /**
     * 获取单个地址的经纬度
     * @param address 完整地址信息
     * @return 经纬度字符串，格式为 "经度,纬度"
     * @throws Exception 可能抛出网络或解析异常
     */
    public static String getGeoCode(String address) throws Exception {
        // 1. 构建请求参数
        Map<String, String> params = new LinkedHashMap<>();
        params.put("address", "河南省许昌市" + address);
        params.put("output", "json");
        params.put("ak", AK);
        
        // 2. 发送HTTP请求
        String responseString = requestGetAK(GEOCODING_URL, params);
        
        // 3. 使用Jackson解析JSON
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(responseString);

        // 4. 检查状态码，0表示成功
        int status = rootNode.path("status").asInt();
        if (status != 0) {
            String message = rootNode.path("message").asText();
            String msg = rootNode.path("msg").asText();
            System.err.println("API响应: " + rootNode.toString());
            String errorMsg = !message.isEmpty() ? message : msg;
            return ("API请求失败: " + errorMsg + ", 状态码: " + status);
        }

        // 6. 提取经纬度信息
        JsonNode locationNode = rootNode.path("result").path("location");
        double lng = locationNode.path("lng").asDouble(); // 经度
        double lat = locationNode.path("lat").asDouble(); // 纬度

        return lng + "," + lat;
    }

    /**
     * 发送HTTP GET请求到百度地图API
     * @param strUrl 基础URL
     * @param param 请求参数
     * @return API响应字符串
     * @throws Exception 网络异常
     */
    private static String requestGetAK(String strUrl, Map<String, String> param) throws Exception {
        if (strUrl == null || strUrl.length() <= 0 || param == null || param.size() <= 0) {
            throw new IllegalArgumentException("URL或参数不能为空");
        }

        StringBuffer queryString = new StringBuffer();
        queryString.append(strUrl);
        if (!strUrl.endsWith("?")) {
            queryString.append("?");
        }
        
        for (Map.Entry<String, String> pair : param.entrySet()) {
            queryString.append(pair.getKey() + "=");
            // 使用Spring的UriUtils进行URL编码
            queryString.append(UriUtils.encode(pair.getValue(), "UTF-8") + "&");
        }

        if (queryString.length() > 0) {
            queryString.deleteCharAt(queryString.length() - 1);
        }

        URL url = new URL(queryString.toString());
        System.out.println("请求URL: " + queryString.toString());
        
        URLConnection httpConnection = (HttpURLConnection) url.openConnection();
        httpConnection.connect();

        InputStreamReader isr = new InputStreamReader(httpConnection.getInputStream(), "UTF-8");
        BufferedReader reader = new BufferedReader(isr);
        StringBuffer buffer = new StringBuffer();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }
        reader.close();
        isr.close();
        
        String response = buffer.toString();
        System.out.println("API响应: " + response);
        return response;
    }

    /**
     * 批量获取地址的经纬度
     * @param addressList 地址列表
     * @return 经纬度结果列表，与输入列表顺序一致
     */
    public static List<String> batchGetGeoCode(List<String> addressList) {
        List<String> results = new ArrayList<>();
        for (String address : addressList) {
            try {
                // 调用单个查询方法
                String result = getGeoCode(address);
                results.add(result);
                System.out.println("地址: " + address + " -> 坐标: " + result);

                // 非常重要：添加延时，避免请求过快被限流 (百度API有QPS限制)
                Thread.sleep(1000); // 延时200毫秒，根据你的配额调整

            } catch (Exception e) {
                e.printStackTrace();
                // 如果某个地址失败，可以记录错误信息并继续下一个
                results.add("查询失败: " + e.getMessage());
            }
        }
        return results;
    }

    // 测试主方法
    public static void main(String[] args) {
        try {
            // 测试单个地址
            System.out.println("=== 单个地址测试 ===");
            String singleResult = getGeoCode("北京市海淀区上地十街10号");
            System.out.println("单个地址结果: " + singleResult);
            
            System.out.println("\n=== 批量地址测试 ===");
            List<String> addresses = new ArrayList<>();
            addresses.add("北京市海淀区上地十街10号");
            addresses.add("上海市浦东新区陆家嘴环路1000号");
            addresses.add("广州市天河区天河路208号");
            addresses.add("河南省许昌市建安区");

            List<String> geoResults = batchGetGeoCode(addresses);

            // 打印结果
            System.out.println("\n批量地理编码结果:");
            for (int i = 0; i < addresses.size(); i++) {
                System.out.println((i+1) + ". " + addresses.get(i) + " -> " + geoResults.get(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}